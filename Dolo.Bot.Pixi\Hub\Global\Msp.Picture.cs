﻿using Dolo.Core.Discord;
using Dolo.Core.Extension;
using Dolo.Planet;
using Dolo.Planet.Enums;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("pic")]
    [Description("get random picture from msp gallery")]
    public async Task LookAsync(SlashCommandContext ctx,
        [Description("player server")] Server server)
    {
        // log command
        await ctx.LogAsync($"/msp picture {server}");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync()) return;

        // check if the shard is ready to use 
        var msp = await ctx.IsShardReadyAsync(server);
        if (msp is null) return;

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **Searching for images ..**");

        // get the id of the user if the connection failed return
        var user = await msp.GetNewPicturesAsync();
        if (!user.Success)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Interaction failed with response `{user.GetStatusCode()}`**");
            return;
        }

        // get random image
        var shuffled = user.Shuffle()
            .First();

        // send response
        await ctx.TryEditResponseAsync(await HubEmbed.PictureAsync(ctx.Guild, shuffled, server));
    }
}