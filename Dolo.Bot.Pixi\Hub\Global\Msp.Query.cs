﻿using Dolo.Core.Discord;
using Dolo.Planet;
using Dolo.Planet.Enums;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("query")]
    [Description("query information about a msp user")]
    public async Task QueryAsync(SlashCommandContext ctx,
        [Description("player server")] Server server,
        [Description("player username")] string username,
        [Description("set to true to get more information")] bool isExtended = false)
    {
        await ctx.LogAsync($"/msp query {username} {server}");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync(true)) return;

        // check if the shard is ready to use 
        var msp = await ctx.IsShardReadyAsync(server);
        if (msp is null) return;

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **Searching for `{username}` ...**");

        var userId = await msp.GetActorIdAsync(username);
        if (!userId.Success)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Interaction failed with response `{userId.GetStatusCode()}`**");
            return;
        }

        if (!userId.IsAvailable)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **User `{username}` from `{server}` is not available.**");
            return;
        }

        // fetching user
        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **Fetching information from `{username}` ...**");

        // get the actor from the msp server return if connection failed
        var user = await msp.GetActorAsync(username, [FetchActorInfo.None]);

        if (!user.Success)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Interaction failed with response `{user.GetStatusCode()}`**");
            return;
        }

        var currentEmbed = await HubEmbed.QueryAsync(ctx.Member!, ctx.Guild!, user, isExtended, true);

        // send message to the chat
        await ctx.TryEditResponseAsync(currentEmbed);
         
        Hub.NebulaClients.TryGetValue(msp.User.Server, out var nebula);
        var fullActor = await msp.GetActorAsync(username, isExtended ? [FetchActorInfo.Artbooks,
            FetchActorInfo.Guestbooks,
            FetchActorInfo.Inventory,
            FetchActorInfo.Looks,
            FetchActorInfo.Movies,
            FetchActorInfo.Pictures,
            FetchActorInfo.Room,
            FetchActorInfo.Status,
            FetchActorInfo.Artbooks,
            FetchActorInfo.Summary,
            FetchActorInfo.Wallposts] : [FetchActorInfo.Summary, FetchActorInfo.Status, FetchActorInfo.Room]);

        if (!fullActor.Success)
            return;

        await ctx.TryEditResponseAsync(await HubEmbed.QueryAsync(ctx.Member!, ctx.Guild!, fullActor, isExtended,false, nebula));

        // add tracking
        // await HubTracking.AddTrackingAsync(fullActor);
    }
}