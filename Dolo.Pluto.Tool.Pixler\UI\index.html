<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>lut<PERSON> - <PERSON><PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Signika:wght@400;600&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@500;600;700&display=swap" rel="stylesheet">
    <script src="tailwind.js"></script>
    <style>
        * {
            outline: none !important;
            box-shadow: none !important;
        }
        
        input:focus,
        select:focus,
        textarea:focus,
        button:focus {
            outline: none !important;
            outline-offset: 0 !important;
            box-shadow: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            appearance: none !important;
        }
        
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none !important;
            margin: 0 !important;
        }
        
        input[type=number] {
            -moz-appearance: textfield !important;
        }
    </style>
</head>

<body class="font-inter m-0 p-0 h-screen overflow-hidden bg-[#1F2023] text-text-main">
    <div class="flex flex-col h-screen">
        <!-- Header -->
        <div class="px-4 py-3 border-b border-border-l1 bg-bg-base flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-7 h-7 rounded-lg bg-bg-surface flex items-center justify-center mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-span-default" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                    </svg>
                </div>
                <h1 class="text-base font-bold font-jakarta text-span-default">Pluto <span class="text-span-muted">Pixler</span></h1>
                <div class="h-5 mx-3 border-r border-border-l1"></div>
                <h2 class="text-[10px] py-0.5 px-2 rounded-full bg-bg-surface text-span-muted font-medium flex items-center">
                    <span class="text-span-default mr-1">v</span>1.0
                </h2>
            </div>
            <div class="flex items-center gap-3">
                <!-- Live Exchange Button -->
                <button id="headerLiveExchange" onclick="toggleHeaderLiveExchange()" class="flex items-center gap-1.5 px-2.5 py-1 rounded-lg
                       bg-bg-surface hover:bg-bg-surface-hover transition-all duration-200 border border-border-l1 shadow-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5 text-span-default" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clip-rule="evenodd" />
                    </svg>
                    <span class="text-xs font-medium text-span-default">Live Exchange</span>
                    <!-- Status indicator dot -->
                    <div id="liveExchangeStatus" class="w-1.5 h-1.5 rounded-full bg-span-muted ml-1"></div>
                </button>

                <!-- Support Button -->
                <a href="https://discord.gg/mspshop" target="_blank" class="flex items-center gap-1.5 px-2.5 py-1 rounded-lg
                           bg-bg-surface hover:bg-bg-surface-hover transition-all duration-200 border border-border-l1 shadow-sm">
                    <svg class="w-3.5 h-3.5 text-span-default" viewBox="0 0 71 55" fill="currentColor">
                        <path d="M60.1045 4.8978C55.5792 2.8214 50.7265 1.2916 45.6527 0.41542C45.5603 0.39851 45.468 0.440769 45.4204 0.525289C44.7963 1.6353 44.105 3.0834 43.6209 4.2216C38.1637 3.4046 32.7345 3.4046 27.3892 4.2216C26.905 3.0581 26.1886 1.6353 25.5617 0.525289C25.5141 0.443589 25.4218 0.40133 25.3294 0.41542C20.2584 1.2888 15.4057 2.8186 10.8776 4.8978C10.8384 4.9147 10.8048 4.9429 10.7825 4.9795C1.57795 18.7309 -0.943561 32.1443 0.293408 45.3914C0.299005 45.4562 0.335386 45.5182 0.385761 45.5576C6.45866 50.0174 12.3413 52.7249 18.1147 54.5195C18.2071 54.5477 18.305 54.5139 18.3638 54.4378C19.7295 52.5728 20.9469 50.6063 21.9907 48.5383C22.0523 48.4172 21.9935 48.2735 21.8676 48.2256C19.9366 47.4931 18.0979 46.6 16.3292 45.5858C16.1893 45.5041 16.1781 45.304 16.3068 45.2082C16.679 44.9293 17.0513 44.6391 17.4067 44.3461C17.471 44.2926 17.5606 44.2813 17.6362 44.3151C29.2558 49.6202 41.8354 49.6202 53.3179 44.3151C53.3935 44.2785 53.4831 44.2898 53.5502 44.3433C53.9057 44.6363 54.2779 44.9293 54.6529 45.2082C54.7816 45.304 54.7732 45.5041 54.6333 45.5858C52.8646 46.6197 51.0259 47.4931 49.0921 48.2228C48.9662 48.2707 48.9102 48.4172 48.9718 48.5383C50.038 50.6034 51.2554 52.5699 52.5959 54.435C52.6519 54.5139 52.7526 54.5477 52.845 54.5195C58.6464 52.7249 64.529 50.0174 70.6019 45.5576C70.6551 45.5182 70.6887 45.459 70.6943 45.3942C72.1747 30.0791 68.2147 16.7757 60.1968 4.9823C60.1772 4.9429 60.1437 4.9147 60.1045 4.8978Z" />
                    </svg>
                    <span class="text-xs font-medium text-span-default">Support</span>
                </a>


            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex overflow-hidden">
            <!-- Content Container -->
            <div class="flex-1 flex flex-col bg-[#1F2023] overflow-hidden">
                <div class="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-white/5">
                    <div class="w-full max-w-4xl mx-auto p-2 sm:p-4 md:p-6 lg:p-8">

                        <!-- Account Setup with Server Selection -->
                        <div class="mb-4">
                            <div class="flex items-center mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-text-main" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd" />
                                </svg>
                                <h3 class="text-xs font-bold text-text-main font-jakarta">Account Setup</h3>
                            </div>

                            <!-- Server Selection -->
                            <div class="bg-bg-surface border border-border-l1 rounded-xl shadow-sm overflow-hidden mb-3">
                                <div class="p-3">
                                    <div class="flex items-center mb-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-text-main" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z" clip-rule="evenodd" />
                                        </svg>
                                        <label class="text-xs font-medium text-text-secondary">Server for Both Accounts</label>
                                    </div>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <select class="w-full bg-bg-surface border border-border-l1 rounded-lg pl-10 pr-10 py-2 text-sm focus:outline-none focus:border-border-l2 transition-all duration-200 text-text-main appearance-none">
                                            <option value="us">🇺🇸 United States</option>
                                            <option value="uk">🇬🇧 United Kingdom</option>
                                            <option value="de">🇩🇪 Germany</option>
                                            <option value="fr">🇫🇷 France</option>
                                            <option value="es">🇪🇸 Spain</option>
                                            <option value="se">🇸🇪 Sweden</option>
                                            <option value="nl">🇳🇱 Netherlands</option>
                                            <option value="au">🇦🇺 Australia</option>
                                        </select>
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                            <svg class="w-4 h-4 text-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 relative">
                                <!-- Account A Login -->
                                <div id="accountA" class="relative bg-bg-surface border border-border-l1 rounded-xl shadow-sm overflow-hidden" data-account="A">
                                    <!-- Ready Overlay (hidden by default) -->
                                    <div id="readyOverlayA" class="absolute inset-0 bg-bg-base/80 backdrop-blur-sm rounded-lg flex items-center justify-center hidden z-10">
                                        <div class="text-center">
                                            <div class="w-8 h-8 rounded-full bg-primary/20 border border-primary/40 flex items-center justify-center mx-auto mb-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <span class="text-sm font-bold text-primary mb-3 block">READY</span>
                                            <button onclick="toggleConnection('A')" class="bg-bg-hover hover:bg-bg-surface text-text-main px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200 flex items-center border border-border-base hover:border-border-l2">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                </svg>
                                                Disconnect
                                            </button>
                                        </div>
                                    </div>
                                    <!-- Account Header Section -->
                                    <div class="p-3 flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-5 h-5 rounded-lg bg-primary/10 border border-primary/30 flex items-center justify-center mr-2">
                                                <span class="text-xs font-bold text-primary">A</span>
                                            </div>
                                            <div>
                                                <h4 class="text-xs font-bold text-text-main font-jakarta">Primary Account</h4>
                                                <div class="flex items-center mt-1">
                                                    <span class="text-xs text-text-secondary">Item sender</span>
                                                    <span class="mx-1.5 text-xs text-text-secondary">•</span>
                                                    <div class="inline-flex items-center text-xs">
                                                        <div id="serverFlagA" class="w-4 h-3 bg-bg-surface border border-border-l1 rounded-sm flex items-center justify-center mr-1">
                                                            <svg class="w-3 h-2" viewBox="0 0 24 16" fill="none">
                                                                <rect width="24" height="16" fill="#B22234"/>
                                                                <rect width="24" height="1.23" y="1.23" fill="white"/>
                                                                <rect width="24" height="1.23" y="3.69" fill="white"/>
                                                                <rect width="24" height="1.23" y="6.15" fill="white"/>
                                                                <rect width="24" height="1.23" y="8.62" fill="white"/>
                                                                <rect width="24" height="1.23" y="11.08" fill="white"/>
                                                                <rect width="24" height="1.23" y="13.54" fill="white"/>
                                                                <rect width="9.6" height="8.62" fill="#3C3B6E"/>
                                                            </svg>
                                                        </div>
                                                        <span class="text-text-secondary">US Server</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="statusA" class="flex items-center px-2 py-1 rounded-md bg-bg-base border border-border-base">
                                            <div class="w-2 h-2 rounded-full bg-error"></div>
                                        </div>
                                    </div>

                                    <!-- Login Form Section -->
                                    <div class="p-3 pt-0 space-y-2">
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <input id="usernameA" type="text" placeholder="Username" class="w-full bg-bg-surface border border-border-l1 rounded-lg pl-10 pr-3 py-2 text-sm focus:outline-none focus:border-border-l2 transition-all duration-200 text-text-main placeholder-text-secondary" />
                                        </div>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <input id="passwordA" type="password" placeholder="Password" class="w-full bg-bg-surface border border-border-l1 rounded-lg pl-10 pr-3 py-2 text-sm focus:outline-none focus:border-border-l2 transition-all duration-200 text-text-main placeholder-text-secondary" />
                                        </div>
                                        <button id="connectA" onclick="toggleConnection('A')" class="w-full bg-bg-surface-hover hover:brightness-110 text-text-main px-4 py-2 rounded-lg text-xs font-medium transition-all duration-200 flex items-center justify-center border border-border-l1 hover:border-border-l2 shadow-sm hover:shadow disabled:bg-bg-base disabled:cursor-not-allowed">
                                            <svg id="connectIconA" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                            </svg>
                                            <span id="connectTextA">Login</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- Transfer Animation -->
                                <div class="hidden sm:flex absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
                                    <!-- Simple Transfer Arrow -->
                                    <div class="relative flex items-center justify-center">
                                        <!-- Arrow Icon -->
                                        <div class="w-8 h-8 bg-bg-surface/90 backdrop-blur-sm border border-border-l1 rounded-lg flex items-center justify-center shadow-sm">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>

                                <!-- Account B Login -->
                                <div id="accountB" class="relative bg-bg-surface border border-border-l1 rounded-xl shadow-sm overflow-hidden" data-account="B">
                                    <!-- Ready Overlay (hidden by default) -->
                                    <div id="readyOverlayB" class="absolute inset-0 bg-bg-base/80 backdrop-blur-sm rounded-lg flex items-center justify-center hidden z-10">
                                        <div class="text-center">
                                            <div class="w-8 h-8 rounded-full bg-warning/20 border border-warning/40 flex items-center justify-center mx-auto mb-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-warning" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <span class="text-sm font-bold text-warning mb-3 block">READY</span>
                                            <button onclick="toggleConnection('B')" class="bg-bg-hover hover:bg-bg-surface text-text-main px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200 flex items-center border border-border-base hover:border-border-l2">
                                                <svg xmlns="http://www.w3.svg/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                </svg>
                                                Disconnect
                                            </button>
                                        </div>
                                    </div>
                                    <!-- Account Header Section -->
                                    <div class="p-3 flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-5 h-5 rounded-lg bg-warning/10 border border-warning/30 flex items-center justify-center mr-2">
                                                <span class="text-xs font-bold text-warning">B</span>
                                            </div>
                                            <div>
                                                <h4 class="text-xs font-bold text-text-main font-jakarta">Secondary Account</h4>
                                                <div class="flex items-center mt-1">
                                                    <span class="text-xs text-text-secondary">Item receiver</span>
                                                    <span class="mx-1.5 text-xs text-text-secondary">•</span>
                                                    <div class="inline-flex items-center text-xs">
                                                        <div id="serverFlagB" class="w-4 h-3 bg-bg-surface border border-border-l1 rounded-sm flex items-center justify-center mr-1">
                                                            <svg class="w-3 h-2" viewBox="0 0 24 16" fill="none">
                                                                <rect width="24" height="16" fill="#B22234"/>
                                                                <rect width="24" height="1.23" y="1.23" fill="white"/>
                                                                <rect width="24" height="1.23" y="3.69" fill="white"/>
                                                                <rect width="24" height="1.23" y="6.15" fill="white"/>
                                                                <rect width="24" height="1.23" y="8.62" fill="white"/>
                                                                <rect width="24" height="1.23" y="11.08" fill="white"/>
                                                                <rect width="24" height="1.23" y="13.54" fill="white"/>
                                                                <rect width="9.6" height="8.62" fill="#3C3B6E"/>
                                                            </svg>
                                                        </div>
                                                        <span class="text-text-secondary">US Server</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="statusB" class="flex items-center px-2 py-1 rounded-md bg-bg-base border border-border-base">
                                            <div class="w-2 h-2 rounded-full bg-error"></div>
                                        </div>
                                    </div>

                                    <!-- Login Form Section -->
                                    <div class="p-3 pt-0 space-y-2">
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <input id="usernameB" type="text" placeholder="Username" class="w-full bg-bg-surface border border-border-l1 rounded-lg pl-10 pr-3 py-2 text-sm focus:outline-none focus:border-border-l2 transition-all duration-200 text-text-main placeholder-text-secondary" />
                                        </div>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <input id="passwordB" type="password" placeholder="Password" class="w-full bg-bg-surface border border-border-l1 rounded-lg pl-10 pr-3 py-2 text-sm focus:outline-none focus:border-border-l2 transition-all duration-200 text-text-main placeholder-text-secondary" />
                                        </div>
                                        <button id="connectB" onclick="toggleConnection('B')" class="w-full bg-bg-surface-hover hover:brightness-110 text-text-main px-4 py-2 rounded-lg text-xs font-medium transition-all duration-200 flex items-center justify-center border border-border-l1 hover:border-border-l2 shadow-sm hover:shadow disabled:bg-bg-base disabled:cursor-not-allowed">
                                            <svg id="connectIconB" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                            </svg>
                                            <span id="connectTextB">Login</span>
                                        </button>
                                    </div>
                                </div>
                            </div>

                        <!-- Item Selection -->
                        <div class="mb-4 mt-2">
                            <div class="flex items-center mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-text-main" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
                                </svg>
                                <h3 class="text-xs font-bold text-text-main font-jakarta">Item Selection</h3>
                            </div>

                            <div class="bg-bg-surface border border-border-l1 rounded-xl shadow-sm overflow-hidden">
                                <!-- Load Items Section -->
                                <div class="p-3 flex items-center gap-2">
                                    <button class="bg-bg-surface-hover hover:brightness-110 text-text-main px-4 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center border border-border-l1 hover:border-border-l2 shadow-sm hover:shadow">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                                        </svg>
                                        Load Items
                                    </button>
                                </div>

                                <!-- Inventory Grid Section -->
                                <div class="px-3 pb-3">
                                    <div class="grid grid-cols-6 gap-2 mb-3 max-h-32 overflow-y-auto">
                                    <!-- Selected Item -->
                                    <div class="bg-bg-hover border border-border-l2 rounded-lg p-2.5 flex flex-col items-center cursor-pointer hover:bg-bg-hover hover:border-border-l2 transition-all duration-200">
                                        <span class="text-lg mb-1">💍</span>
                                        <span class="text-xs text-text-main text-center leading-tight font-medium">Diamond Ring</span>
                                    </div>

                                    <!-- Available Items -->
                                    <div class="bg-bg-base border border-border-base rounded-lg p-2.5 flex flex-col items-center cursor-pointer hover:bg-bg-hover hover:border-border-l2 transition-all duration-200">
                                        <span class="text-lg mb-1">📿</span>
                                        <span class="text-xs text-text-secondary text-center leading-tight hover:text-text-main">Gold Necklace</span>
                                    </div>

                                    <div class="bg-bg-base border border-border-base rounded-lg p-2.5 flex flex-col items-center cursor-pointer hover:bg-bg-hover hover:border-border-l2 transition-all duration-200">
                                        <span class="text-lg mb-1">💎</span>
                                        <span class="text-xs text-text-secondary text-center leading-tight hover:text-text-main">Ruby Earrings</span>
                                    </div>

                                    <div class="bg-bg-base border border-border-base rounded-lg p-2.5 flex flex-col items-center cursor-pointer hover:bg-bg-hover hover:border-border-l2 transition-all duration-200">
                                        <span class="text-lg mb-1">🔗</span>
                                        <span class="text-xs text-text-secondary text-center leading-tight hover:text-text-main">Silver Bracelet</span>
                                    </div>

                                    <div class="bg-bg-base border border-border-base rounded-lg p-2.5 flex flex-col items-center cursor-pointer hover:bg-bg-hover hover:border-border-l2 transition-all duration-200">
                                        <span class="text-lg mb-1">🟢</span>
                                        <span class="text-xs text-text-secondary text-center leading-tight hover:text-text-main">Emerald Pendant</span>
                                    </div>

                                    <div class="bg-bg-base border border-border-base rounded-lg p-2.5 flex flex-col items-center cursor-pointer hover:bg-bg-hover hover:border-border-l2 transition-all duration-200">
                                        <span class="text-lg mb-1">👑</span>
                                        <span class="text-xs text-text-secondary text-center leading-tight hover:text-text-main">Crystal Tiara</span>
                                    </div>
                                    </div>

                                    <!-- Selected Item Display & Start Button -->
                                    <div class="flex items-center gap-3 pt-2 border-t border-border-strong">
                                    <div class="flex items-center flex-1">
                                        <div class="w-8 h-8 bg-bg-surface border border-primary rounded-lg flex items-center justify-center mr-2">
                                            <span class="text-sm">💍</span>
                                        </div>
                                        <div>
                                            <div class="text-xs font-medium text-text-main">Diamond Ring</div>
                                            <div class="text-xs text-text-secondary">Selected for exchange</div>
                                        </div>
                                    </div>
                                    <button class="bg-bg-surface-hover hover:brightness-110 text-text-main px-4 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center border border-border-l1 hover:border-border-l2 shadow-sm hover:shadow">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        Start Exchange
                                    </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Floating Exchange Preview Window -->
        <div id="exchangePreview" class="fixed bottom-4 left-4 w-64 bg-bg-surface border border-border-base rounded-lg shadow-2xl z-50 transition-transform duration-300 ease-out">
            <!-- Header (Clickable to hide) -->
            <div id="exchangeHeader" class="flex items-center justify-between p-3 bg-bg-base rounded-t-lg border-b border-border-base cursor-pointer hover:bg-bg-hover transition-all duration-200" onclick="hideExchangePreview()">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1.5 text-text-main" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clip-rule="evenodd" />
                    </svg>
                    <h4 class="text-xs font-bold text-text-main">Live Exchange</h4>
                </div>
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-text-secondary hover:text-text-main transition-colors" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>

            <!-- Content (Expanded by default) -->
            <div id="exchangeContent" class="overflow-hidden" style="max-height: 400px; padding: 12px;">
                <!-- Current Item & Status -->
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <div class="w-6 h-6 bg-bg-hover border border-border-base rounded-lg flex items-center justify-center mr-2">
                            <span class="text-xs">💍</span>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-text-main">Diamond Ring</div>
                            <div class="text-[10px] text-text-secondary">Round 3 of 10</div>
                        </div>
                    </div>
                    <div class="flex items-center text-[10px]">
                        <div class="w-2 h-2 rounded-full bg-green-500 mr-1"></div>
                        <span class="text-text-main">Active</span>
                    </div>
                </div>

                <!-- Vertical Account Flow -->
                <div class="relative">
                    <!-- Account A -->
                    <div class="bg-bg-base border border-border-base rounded-lg p-2 relative z-10">
                        <div class="flex items-center justify-between mb-1">
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-lg bg-primary/10 border border-primary/30 flex items-center justify-center mr-1.5">
                                    <span class="text-[8px] font-bold text-primary">A</span>
                                </div>
                                <span class="text-xs text-text-main">Primary</span>
                            </div>
                            <div class="flex items-center text-[10px]">
                                <div class="w-2 h-2 rounded-full bg-warning mr-1"></div>
                                <span class="text-warning">Sending</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-center border border-dashed border-warning/30 rounded-lg min-h-[24px] bg-bg-hover">
                            <span class="text-xs">💍</span>
                        </div>
                        <div class="text-center mt-1">
                            <span class="text-[10px] text-text-secondary">3 sent • 2 received</span>
                        </div>
                    </div>

                    <!-- Data Transfer Overlay -->
                    <div class="absolute left-1/2 transform -translate-x-1/2 z-20" style="top: 50%; height: 32px; margin-top: -16px;">
                        <div class="relative w-4 h-full flex flex-col items-center justify-center">
                            <!-- Transfer line background -->
                            <div class="absolute w-0.5 h-full bg-border-base opacity-50"></div>

                            <!-- Animated data packets -->
                            <div id="dataPackets" class="absolute w-full h-full">
                                <!-- Multiple animated dashes will be inserted here by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Account B -->
                    <div class="bg-bg-base border border-border-base rounded-lg p-2 relative z-10 mt-2">
                        <div class="flex items-center justify-between mb-1">
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-lg bg-warning/10 border border-warning/30 flex items-center justify-center mr-1.5">
                                    <span class="text-[8px] font-bold text-warning">B</span>
                                </div>
                                <span class="text-xs text-text-main">Secondary</span>
                            </div>
                            <div class="flex items-center text-[10px]">
                                <div class="w-2 h-2 rounded-full bg-text-secondary mr-1"></div>
                                <span class="text-text-secondary">Waiting</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-center border border-dashed border-border-base rounded-lg min-h-[24px] bg-bg-hover">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-2.5 w-2.5 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
                            </svg>
                        </div>
                        <div class="text-center mt-1">
                            <span class="text-[10px] text-text-secondary">2 sent • 3 received</span>
                        </div>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-[10px] text-text-secondary">Progress</span>
                        <span class="text-[10px] text-text-secondary">30%</span>
                    </div>
                    <div class="w-full bg-bg-base rounded-full h-1 border border-border-base">
                        <div class="bg-gradient-to-r from-warning to-primary h-1 rounded-full transition-all duration-500" style="width: 30%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const serverFlags = {
            'us': '<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="16" fill="#B22234"/><rect width="24" height="1.23" y="1.23" fill="white"/><rect width="24" height="1.23" y="3.69" fill="white"/><rect width="24" height="1.23" y="6.15" fill="white"/><rect width="24" height="1.23" y="8.62" fill="white"/><rect width="24" height="1.23" y="11.08" fill="white"/><rect width="24" height="1.23" y="13.54" fill="white"/><rect width="9.6" height="8.62" fill="#3C3B6E"/></svg>',
            'uk': '<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="16" fill="#012169"/><path d="M0 0l24 16M24 0L0 16" stroke="white" stroke-width="1.6"/><path d="M0 0l24 16M24 0L0 16" stroke="#C8102E" stroke-width="1"/></svg>',
            'de': '<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="5.33" fill="black"/><rect width="24" height="5.33" y="5.33" fill="#DD0000"/><rect width="24" height="5.33" y="10.67" fill="#FFCE00"/></svg>',
            'fr': '<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="8" height="16" fill="#002395"/><rect width="8" height="16" x="8" fill="white"/><rect width="8" height="16" x="16" fill="#ED2939"/></svg>',
            'es': '<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="4" fill="#AA151B"/><rect width="24" height="8" y="4" fill="#F1BF00"/><rect width="24" height="4" y="12" fill="#AA151B"/></svg>',
            'se': '<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="16" fill="#006AA7"/><rect width="24" height="2" y="7" fill="#FECC00"/><rect width="2" height="16" x="7" fill="#FECC00"/></svg>',
            'nl': '🇳🇱',
            'au': '🇦🇺'
        };

        function updateServerFlags() {
            const serverSelect = document.querySelector('select');
            const selectedServer = serverSelect.value;
            const flag = serverFlags[selectedServer];
            
            document.getElementById('serverFlagA').textContent = flag;
            document.getElementById('serverFlagB').textContent = flag;
        }

        function setupAccountLogin(accountId) {
            const connectBtn = document.getElementById(`connect${accountId}`);
            const accountBox = document.getElementById(`account${accountId}`);
            const statusBox = document.getElementById(`status${accountId}`);
            const usernameInput = document.getElementById(`username${accountId}`);
            const passwordInput = document.getElementById(`password${accountId}`);

            // Remove old event listener - now handled by onclick in HTML
        }

        // Simulate live exchange status updates
        function simulateExchangeProgress() {
            const states = [
                {
                    accountA: { status: 'Sending', statusColor: 'warning', hasItem: true, itemStatus: 'Transferring...', arrow: true },
                    accountB: { status: 'Waiting', statusColor: 'text-secondary', hasItem: false, itemStatus: 'Diamond Ring incoming', arrow: false },
                    progress: 30,
                    round: 3,
                    direction: 'A → B'
                },
                {
                    accountA: { status: 'Waiting', statusColor: 'text-secondary', hasItem: false, itemStatus: 'Waiting for return', arrow: false },
                    accountB: { status: 'Received', statusColor: 'success', hasItem: true, itemStatus: 'Processing...', arrow: false },
                    progress: 35,
                    round: 3,
                    direction: 'Processing'
                },
                {
                    accountA: { status: 'Waiting', statusColor: 'text-secondary', hasItem: false, itemStatus: 'Diamond Ring incoming', arrow: false },
                    accountB: { status: 'Sending', statusColor: 'warning', hasItem: true, itemStatus: 'Transferring...', arrow: true },
                    progress: 40,
                    round: 4,
                    direction: 'B → A'
                },
                {
                    accountA: { status: 'Received', statusColor: 'success', hasItem: true, itemStatus: 'Processing...', arrow: false },
                    accountB: { status: 'Waiting', statusColor: 'text-secondary', hasItem: false, itemStatus: 'Waiting for next round', arrow: false },
                    progress: 45,
                    round: 4,
                    direction: 'Processing'
                }
            ];

            let currentState = 0;

            setInterval(() => {
                const state = states[currentState];

                // Update Account A
                const accountAStatus = document.querySelector('#accountA').parentElement.querySelector('[class*="Live Exchange Status"]')?.parentElement.querySelector('.grid').children[0];
                if (accountAStatus) {
                    const statusElement = accountAStatus.querySelector('.flex.items-center.text-xs');
                    const itemContainer = accountAStatus.querySelector('.border-dashed');
                    const arrow = accountAStatus.querySelector('.absolute');

                    if (statusElement) {
                        statusElement.innerHTML = `
                            <div class="w-1 h-1 rounded-full bg-${state.accountA.statusColor} mr-1 ${state.accountA.statusColor === 'warning' ? 'animate-pulse' : ''}"></div>
                            <span class="text-${state.accountA.statusColor}">${state.accountA.status}</span>
                        `;
                    }

                    if (itemContainer) {
                        itemContainer.className = `flex-1 flex items-center justify-center border-2 border-dashed ${state.accountA.hasItem ? 'border-warning/30' : 'border-border-base'} rounded-lg min-h-[60px] bg-[#1F2023] relative`;
                        itemContainer.innerHTML = `
                            <div class="text-center">
                                <div class="w-6 h-6 mx-auto mb-1 rounded-md ${state.accountA.hasItem ? 'bg-warning/20 border-warning/40' : 'bg-[#2E3033] border-border-strong'} border flex items-center justify-center">
                                    ${state.accountA.hasItem ? '<span class="text-sm">💍</span>' : '<svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-text-secondary" viewBox="0 0 20 20" fill="currentColor"><path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" /></svg>'}
                                </div>
                                <p class="text-xs text-text-main mb-0.5">${state.accountA.hasItem ? 'Diamond Ring' : 'Ready to receive'}</p>
                                <p class="text-[10px] text-${state.accountA.hasItem ? 'warning' : 'text-secondary'}">${state.accountA.itemStatus}</p>
                            </div>
                            ${state.accountA.arrow ? '<div class="absolute -right-2 top-1/2 transform -translate-y-1/2"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-warning animate-pulse" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" /></svg></div>' : ''}
                        `;
                    }
                }

                // Update progress bar
                const progressBar = document.querySelector('.bg-gradient-to-r');
                const roundText = document.querySelector('[class*="Round"]');
                const directionText = document.querySelector('[class*="→"]');
                const percentText = document.querySelector('[class*="Complete"]');

                if (progressBar) progressBar.style.width = `${state.progress}%`;
                if (roundText) roundText.textContent = `Round ${state.round} of 10`;
                if (directionText) directionText.textContent = state.direction;
                if (percentText) percentText.textContent = `${state.progress}% Complete`;

                currentState = (currentState + 1) % states.length;
            }, 3000); // Update every 3 seconds
        }

        // Floating exchange preview controls
        function hideExchangePreview() {
            const floatingWidget = document.getElementById('exchangePreview');
            const headerButton = document.getElementById('headerLiveExchange');
            const statusDot = document.getElementById('liveExchangeStatus');

            // Animate widget sliding down
            floatingWidget.style.transform = 'translateY(100%)';

            // Hide widget after animation completes
            setTimeout(() => {
                floatingWidget.style.display = 'none';
                floatingWidget.style.transform = 'translateY(0)'; // Reset for next show
            }, 300);

            // Update header button state back to inactive
            headerButton.classList.remove('bg-[#3A3C3F]');
            headerButton.classList.add('bg-[#2E3033]');
            statusDot.classList.remove('bg-success', 'animate-pulse');
            statusDot.classList.add('bg-text-secondary');
        }

        // Clean data transfer animation
        let transferAnimationInterval;

        function createDataDash(direction) {
            const container = document.getElementById('dataPackets');
            if (!container) return;

            const dash = document.createElement('div');
            dash.className = 'absolute w-0.5 h-1 bg-warning rounded-full';
            dash.style.left = '50%';
            dash.style.transform = 'translateX(-50%)';

            if (direction === 'down') {
                dash.style.top = '0px';
            } else {
                dash.style.top = '32px';
            }

            container.appendChild(dash);

            // Simple CSS transition animation
            dash.style.transition = 'top 1s linear, opacity 1s linear';

            setTimeout(() => {
                if (direction === 'down') {
                    dash.style.top = '32px';
                } else {
                    dash.style.top = '0px';
                }
                dash.style.opacity = '0.3';
            }, 50);

            // Remove dash after animation
            setTimeout(() => {
                if (dash.parentNode) {
                    dash.parentNode.removeChild(dash);
                }
            }, 1100);
        }

        function startDataTransfer(direction) {
            // Clear existing animation
            if (transferAnimationInterval) {
                clearInterval(transferAnimationInterval);
            }

            // Create dashes at regular intervals
            transferAnimationInterval = setInterval(() => {
                createDataDash(direction);
            }, 200);

            // Create first dash immediately
            createDataDash(direction);
        }

        // Auto-demo: Switch transfer direction every 4 seconds
        let transferDirection = true;
        setInterval(() => {
            if (transferDirection) {
                startDataTransfer('down'); // A → B
            } else {
                startDataTransfer('up'); // B → A
            }
            transferDirection = !transferDirection;
        }, 4000);

        // Start initial transfer
        setTimeout(() => {
            startDataTransfer('down');
        }, 500);

        // Connection state management
        let connectionStates = {
            A: false,
            B: false
        };

        async function toggleConnection(account) {
            const isConnected = connectionStates[account];
            const button = document.getElementById(`connect${account}`);
            const icon = document.getElementById(`connectIcon${account}`);
            const text = document.getElementById(`connectText${account}`);
            const status = document.getElementById(`status${account}`);
            const usernameInput = document.getElementById(`username${account}`);
            const passwordInput = document.getElementById(`password${account}`);
            const accountBox = document.querySelector(`[data-account="${account}"]`);

            if (isConnected) {
                // Disconnect
                connectionStates[account] = false;

                // Update button to Connect state (original gray)
                button.className = "w-full bg-[#3A3C3F] hover:bg-[#4A4C4F] disabled:bg-[#2E3033] disabled:cursor-not-allowed text-text-main px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center justify-center";
                button.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor" id="connectIcon${account}">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span id="connectText${account}">Connect</span>
                `;

                // Update status to red dot
                status.innerHTML = '<div class="w-2 h-2 rounded-full bg-error"></div>';

                // Enable inputs
                usernameInput.disabled = false;
                passwordInput.disabled = false;
                usernameInput.classList.remove('opacity-50');
                passwordInput.classList.remove('opacity-50');

                // Hide ready overlay
                const readyOverlay = document.getElementById(`readyOverlay${account}`);
                if (readyOverlay) {
                    readyOverlay.classList.add('hidden');
                }

            } else {
                // Validate inputs before connecting
                const username = usernameInput.value.trim();
                const password = passwordInput.value.trim();

                if (!username || !password) {
                    alert('Please enter both username and password');
                    return;
                }

                // Set connecting state
                button.disabled = true;
                button.innerHTML = `
                    <svg class="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Connecting...</span>
                `;

                // Simulate connection delay
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Connect
                connectionStates[account] = true;

                // Disable login button when connected
                button.disabled = true;
                button.className = "w-full bg-bg-hover hover:bg-bg-surface text-text-main px-4 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center justify-center border border-border-strong hover:border-border-l2 disabled:bg-bg-base disabled:cursor-not-allowed disabled:opacity-50";
                button.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor" id="connectIcon${account}">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                    </svg>
                    <span id="connectText${account}">Login</span>
                `;

                // Update status to green dot
                status.innerHTML = '<div class="w-2 h-2 rounded-full bg-green-500"></div>';

                // Disable inputs
                usernameInput.disabled = true;
                passwordInput.disabled = true;
                usernameInput.classList.add('opacity-50');
                passwordInput.classList.add('opacity-50');

                // Show ready overlay
                const readyOverlay = document.getElementById(`readyOverlay${account}`);
                if (readyOverlay) {
                    readyOverlay.classList.remove('hidden');
                }
            }

            // Update Live Exchange status
            updateLiveExchangeStatus();
        }

        // Update flag displays when server changes
        function updateServerFlags(selectedValue) {
            const flagA = document.getElementById('serverFlagA');
            const flagB = document.getElementById('serverFlagB');

            if (serverFlags[selectedValue]) {
                flagA.innerHTML = serverFlags[selectedValue];
                flagB.innerHTML = serverFlags[selectedValue];
            }
        }

        // Listen for server selection changes
        document.addEventListener('DOMContentLoaded', function() {
            const serverSelect = document.querySelector('select');
            if (serverSelect) {
                serverSelect.addEventListener('change', function() {
                    updateServerFlags(this.value);
                });
            }
        });

        // Simplified exchange status updates for floating window
        function updateFloatingExchange() {
            const states = [
                {
                    accountA: { status: 'Sending', statusColor: 'warning', hasItem: true, arrow: true },
                    accountB: { status: 'Waiting', statusColor: 'text-secondary', hasItem: false, arrow: false },
                    progress: 30,
                    round: 3
                },
                {
                    accountA: { status: 'Waiting', statusColor: 'text-secondary', hasItem: false, arrow: false },
                    accountB: { status: 'Received', statusColor: 'success', hasItem: true, arrow: false },
                    progress: 35,
                    round: 3
                },
                {
                    accountA: { status: 'Waiting', statusColor: 'text-secondary', hasItem: false, arrow: false },
                    accountB: { status: 'Sending', statusColor: 'warning', hasItem: true, arrow: true },
                    progress: 40,
                    round: 4
                },
                {
                    accountA: { status: 'Received', statusColor: 'success', hasItem: true, arrow: false },
                    accountB: { status: 'Waiting', statusColor: 'text-secondary', hasItem: false, arrow: false },
                    progress: 45,
                    round: 4
                }
            ];

            let currentState = 0;

            setInterval(() => {
                const state = states[currentState];

                // Update floating window content
                const floatingWindow = document.getElementById('exchangePreview');
                if (floatingWindow && floatingWindow.style.display !== 'none') {
                    // Update round info
                    const roundElement = floatingWindow.querySelector('.text-\\[10px\\].text-text-secondary');
                    if (roundElement) {
                        roundElement.textContent = `Round ${state.round} of 10`;
                    }

                    // Update progress bar
                    const progressBar = floatingWindow.querySelector('.bg-gradient-to-r');
                    if (progressBar) {
                        progressBar.style.width = `${state.progress}%`;
                    }

                    const progressText = floatingWindow.querySelector('.text-\\[10px\\].text-text-secondary:last-child');
                    if (progressText) {
                        progressText.textContent = `${state.progress}%`;
                    }
                }

                currentState = (currentState + 1) % states.length;
            }, 3000);
        }

        // Transfer Animation
        function startTransferAnimation() {
            const transferItem = document.getElementById('transferItem');
            if (!transferItem) return;

            // Reset position
            transferItem.style.left = '0';
            transferItem.style.transform = 'translateX(0)';

            // Animate to the right
            setTimeout(() => {
                transferItem.style.transform = 'translateX(48px)'; // Move across the 16*3 = 48px width
            }, 100);

            // Reset after animation
            setTimeout(() => {
                transferItem.style.left = '0';
                transferItem.style.transform = 'translateX(0)';
            }, 2100);
        }

        // Start transfer animation periodically
        function initTransferAnimation() {
            // Start immediately
            startTransferAnimation();

            // Repeat every 4 seconds
            setInterval(startTransferAnimation, 4000);
        }

        // Initialize both accounts and server selection
        document.addEventListener('DOMContentLoaded', () => {
            setupAccountLogin('A');
            setupAccountLogin('B');

            // Setup server selection change handler
            const serverSelect = document.querySelector('select');
            if (serverSelect) {
                serverSelect.addEventListener('change', updateServerFlags);
            }

            // Initialize flags
            updateServerFlags();

            // Start floating exchange updates (but window stays hidden until clicked)
            setTimeout(updateFloatingExchange, 2000);

            // Start transfer animation
            setTimeout(initTransferAnimation, 1000);

            // Add hover effect enhancement to exchange trigger
            const exchangeTrigger = document.getElementById('exchangeTrigger');
            if (exchangeTrigger) {
                exchangeTrigger.addEventListener('mouseenter', () => {
                    exchangeTrigger.style.transform = 'translateY(-1px)';
                    exchangeTrigger.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                });

                exchangeTrigger.addEventListener('mouseleave', () => {
                    exchangeTrigger.style.transform = 'translateY(0)';
                    exchangeTrigger.style.boxShadow = 'none';
                });
            }

            // Initialize floating widget as hidden
            document.getElementById('exchangePreview').style.display = 'none';
        });

        // Header Live Exchange toggle functionality
        function toggleHeaderLiveExchange() {
            const floatingWidget = document.getElementById('exchangePreview');
            const headerButton = document.getElementById('headerLiveExchange');
            const statusDot = document.getElementById('liveExchangeStatus');

            if (floatingWidget.style.display === 'none') {
                // Show the floating widget with slide up animation
                floatingWidget.style.display = 'block';
                floatingWidget.style.transform = 'translateY(100%)';

                // Animate sliding up
                setTimeout(() => {
                    floatingWidget.style.transform = 'translateY(0)';
                }, 10);

                // Update header button state
                headerButton.classList.add('bg-[#3A3C3F]');
                headerButton.classList.remove('bg-[#2E3033]');
                statusDot.classList.remove('bg-text-secondary');
                statusDot.classList.add('bg-success', 'animate-pulse');

            } else {
                // Hide the floating widget
                hideExchangePreview();
            }
        }

        // Update Live Exchange status based on connection states
        function updateLiveExchangeStatus() {
            const statusDot = document.getElementById('liveExchangeStatus');
            const bothConnected = connectionStates.A && connectionStates.B;

            if (bothConnected) {
                statusDot.classList.remove('bg-text-secondary');
                statusDot.classList.add('bg-warning', 'animate-pulse');
            } else {
                statusDot.classList.remove('bg-warning', 'animate-pulse');
                statusDot.classList.add('bg-text-secondary');
            }
        }
    </script>
</body>

</html>
