using System.Text;
using Dolo.Core.Cryption;
using Dolo.Core.Http;
using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.Shard.Services.License;

public class LicenseValidationService(IAppConfiguration appConfig, ILogger<LicenseValidationService> logger) : ILicenseValidationService
{
    private readonly string _licenseUrl = $"{appConfig.ApiDomain}api/toolbox/{appConfig.AppId}/license/validate";

    public async Task<bool> ValidateTokenAsync(string token)
    {
        try
        {
            var encryptedToken = CustomEncryptor.DoEncrypt(token);
            if (string.IsNullOrEmpty(encryptedToken))
                return false;

            var request = await Http.TrySendAsync<string>(cfg =>
            {
                cfg.Content = new StringContent(encryptedToken, Encoding.UTF8, "text/plain");
                cfg.Method = HttpMethod.Post;
                cfg.Url = _licenseUrl;
            });

            if (!request.IsSuccess)
            {
                logger.LogWarning("License validation failed: {StatusCode} - {Error}",
                    request.StatusCode, request.Exception?.Message);
                return false;
            }

            var decryptedResponse = CustomEncryptor.DoDecrypt(request.Body);
            return decryptedResponse == "true";
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Exception during license validation");
            return false;
        }
    }
}
