﻿using Dolo.Core.Consola;
using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.System;

public static class VoiceSystem
{
    public static async Task StartAsync()
    {
        if (Hub.Guild is null)
            return;

        Consola.Information("Voice system started.");
        _ = Task.Run(async () => {
            var timr = new PeriodicTimer(new(0, 0, 30));
            while (await timr.WaitForNextTickAsync())
                try
                {
                    var members = Hub.Guild.Members.Where(a => a.Value.VoiceState != null);
                    foreach (var member in members)
                    {
                        // check if user is muted, deafed or channel is 1 user only also check if the user with the bot alone
                        if (
                            member.Value.VoiceState.IsSelfMuted             ||
                            member.Value.VoiceState.IsSelfDeafened          ||
                            member.Value.VoiceState.IsServerDeafened        ||
                            member.Value.VoiceState.IsServerMuted           ||
                            member.Value.VoiceState.Channel.Users.Count < 2 ||
                            member.Value.VoiceState.Channel.Users.Count == 2 &&
                            member.Value.VoiceState.Channel.Users.Any(a => a.IsBot)
                        ) continue;

                        // get first entry
                        var user = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == member.Value.Id);
                        if (user is null)
                            continue;

                        // set random points
                        user.Level.Points += RandomNumberGenerator.GetInt32(0, 5) == 3 ? 1 : 0;

                        await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, member.Value.Id), Builders<ServerMember>.Update
                            .Set(a => a.Level.Points, user.Level.Points));
                    }
                }
                catch (Exception ex)
                {
                    ex.Log();
                }
        });
    }
}
