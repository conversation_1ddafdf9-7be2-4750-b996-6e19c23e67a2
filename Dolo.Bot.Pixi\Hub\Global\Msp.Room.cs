﻿using Dolo.Core.Discord;
using Dolo.Planet;
using Dolo.Planet.Enums;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("room")]
    [Description("get room of a player")]
    public async Task RoomAsync(SlashCommandContext ctx,
        [Description("player server")] Server server,
        [Description("player username")] string username)
    {
        // log command
        await ctx.LogAsync($"/msp room {username} {server}");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync()) return;

        // check if the shard is ready to use 
        var msp = await ctx.IsShardReadyAsync(server);
        if (msp is null) return;

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **Searching for `{username}` ..**");

        // get the id of the user if the connection failed return
        var user = await msp.GetActorIdAsync(username);
        if (!user.Success)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Interaction failed with response `{user.HttpResponse?.StatusCode}`**");
            return;
        }

        // return if the user is not available
        if (!user.IsAvailable)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » User `{username}` from `{server}` is not available.");
            return;
        }

        var room = await msp.GetActorRoomAsync(user.Id);
        if (!room.Success)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Interaction failed with response `{room.GetStatusCode()}`**");
            return;
        }

        // send response
        await ctx.TryEditResponseAsync(""                                                        +
                                       $"{HubEmoji.Pluto} » **{user.Username}** (`{server}`) \n" +
                                       $"{HubEmoji.Pluto} » `{room.Likes}` {HubEmoji.MspHeart}");
        await ctx.Channel.TrySendMessageAsync(room.RoomUrl);
    }
}