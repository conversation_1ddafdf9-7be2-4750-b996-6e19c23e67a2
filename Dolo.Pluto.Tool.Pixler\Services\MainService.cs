using System;
using Dolo.Pluto.Shard;
using Dolo.Pluto.Tool.Pixler.Components.Content;

namespace Dolo.Pluto.Tool.Pixler.Services;

public class MainService : IService, IMainService {

    public Main Main { get; set; } = default!;
    public Shard.Toolbox.Tool? Tool { get; set; }

    public void StateHasChangedAsync() => Main.StateHasChangedAsync();

    // Application state properties can be added here if needed
    public bool IsInitialized { get; set; } = true; // Set to true to skip initialization screen
}
