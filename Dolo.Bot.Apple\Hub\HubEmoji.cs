﻿namespace Dolo.Bot.Apple.Hub;

public static class HubEmoji
{
    public static DiscordEmoji? Pluto => Hub.Guild?.Emojis[959534436817453097];
    public static DiscordEmoji? Female => Hub.Guild?.Emojis[828640785364156442];
    public static DiscordEmoji? Male => Hub.Guild?.Emojis[828640785155096586];
    public static DiscordEmoji? WhiteFlyingHeart => Hub.Guild?.Emojis[824056282440400906];
    public static DiscordEmoji? WhiteHeart => Hub.Guild?.Emojis[823974911189581873];
    public static DiscordEmoji? WhitePopHeart => Hub.Guild?.Emojis[824056282196869191];
    public static DiscordEmoji? MspLoading => Hub.Guild?.Emojis[864609524341407764];
    public static DiscordEmoji? Shrug => Hub.Guild?.Emojis[824012313492324412];
    public static DiscordEmoji? GhostLove => Hub.Guild?.Emojis[896171380507287552];
    public static DiscordEmoji? Yes => Hub.Guild?.Emojis[823974901173977119];
    public static DiscordEmoji? No => Hub.Guild?.Emojis[823991971425157131];
    public static DiscordEmoji? FaceOh => Hub.Guild?.Emojis[829735334802751508];
    public static DiscordEmoji? CheckYes => Hub.Guild?.Emojis[824615299818323999];
    public static DiscordEmoji? CheckNo => Hub.Guild?.Emojis[824615298963341333];
    public static DiscordEmoji? WhiteCrown => Hub.Guild?.Emojis[828255588034936872];
    public static DiscordEmoji? Astro => Hub.Guild?.Emojis[960572938673852486];
    public static DiscordEmoji? Party => Hub.Guild?.Emojis[823992130493087805];
    public static DiscordEmoji? RosePulse => Hub.Guild?.Emojis[828361752894767144];
    public static DiscordEmoji? PinkDot => Hub.Guild?.Emojis[1076816662998831164];
    public static DiscordEmoji? BluetDot => Hub.Guild?.Emojis[1076816657747554344];
    public static DiscordEmoji? ApplePlace => Hub.Guild?.Emojis[1076816659609821267];
    public static DiscordEmoji? Apple => DiscordEmoji.FromGuildEmote(Hub.Discord, 816050523765211169);
    public static DiscordEmoji? France => DiscordEmoji.FromGuildEmote(Hub.Discord, 752634877806903337);
    public static DiscordEmoji? Poland => DiscordEmoji.FromGuildEmote(Hub.Discord, 752634878226464789);
    public static DiscordEmoji? Turkey => DiscordEmoji.FromGuildEmote(Hub.Discord, 752634878880776232);
    public static DiscordEmoji? Germany => DiscordEmoji.FromGuildEmote(Hub.Discord, 752634877945315369);
    public static DiscordEmoji? Cake => DiscordEmoji.FromGuildEmote(Hub.Discord, 963119113713111110);
    public static DiscordEmoji? Fame => DiscordEmoji.FromGuildEmote(Hub.Discord, 806006180707041291);
    public static DiscordEmoji? MspHeart => DiscordEmoji.FromGuildEmote(Hub.Discord, 979352003895508992);
    public static DiscordEmoji? IceCube => DiscordEmoji.TryFromName(Hub.Discord, ":ice_cube:", out var emoji) ? emoji : default;
    public static DiscordEmoji? Changelog => IceCube;
    public static DiscordEmoji? News => DiscordEmoji.TryFromName(Hub.Discord, ":round_pushpin:", out var emoji) ? emoji : default;
    public static DiscordEmoji? Tada => DiscordEmoji.TryFromName(Hub.Discord, ":tada:", out var emoji) ? emoji : default;
    public static DiscordEmoji? Key => DiscordEmoji.TryFromName(Hub.Discord, ":key:", out var emoji) ? emoji : default;
    public static DiscordEmoji? TongueOut => DiscordEmoji.TryFromName(Hub.Discord, ":stuck_out_tongue_closed_eyes:", out var emoji) ? emoji : default;
}