using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.Shard.Services.License;

public class LicenseFileService(IAppConfiguration appConfig, ILogger<LicenseFileService> logger) : ILicenseFileService
{
    private readonly string _licenseFilePath = $"{appConfig.AppId}.lic";

    public bool LicenseFileExists() => File.Exists(_licenseFilePath);

    public async Task<string?> ReadLicenseFileAsync()
    {
        if (!LicenseFileExists()) return null;

        try
        {
            using var reader = new StreamReader(_licenseFilePath);
            var content = await reader.ReadToEndAsync();
            return string.IsNullOrEmpty(content) ? null : content;
        }
        catch (IOException ex)
        {
            logger.LogError(ex, "Error reading license file");
            return null;
        }
    }

    public async Task WriteLicenseFileAsync(string token)
    {
        try
        {
            using var writer = new StreamWriter(_licenseFilePath, false);
            await writer.WriteAsync(token);
            logger.LogInformation("License saved successfully");
        }
        catch (IOException ex)
        {
            logger.LogError(ex, "Error writing license file");
            throw;
        }
    }

    public Task DeleteLicenseFileAsync()
    {
        if (!LicenseFileExists()) return Task.CompletedTask;

        try
        {
            File.Delete(_licenseFilePath);
            logger.LogInformation("Deleted invalid license file");
            return Task.CompletedTask;
        }
        catch (IOException ex)
        {
            logger.LogError(ex, "Could not delete invalid license file");
            throw;
        }
    }
}
