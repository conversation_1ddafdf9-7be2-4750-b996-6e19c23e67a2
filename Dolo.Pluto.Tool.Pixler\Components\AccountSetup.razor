<!-- Account Setup with Server Selection -->
<div class="mb-4">
    <div class="flex items-center mb-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-text-main" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd" />
        </svg>
        <h3 class="text-xs font-bold text-text-main font-jakarta">Account Setup</h3>
    </div>

    <!-- Server Selection -->
    <div class="bg-[#2E3033] border border-border-strong rounded-lg p-3 mb-3">
        <div class="flex items-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-text-main" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z" clip-rule="evenodd" />
            </svg>
            <label class="text-xs font-medium text-text-secondary">Server for Both Accounts</label>
        </div>
        <div class="relative">
            <select @bind="SelectedServer" class="w-full bg-[#1F2023] border border-border-base rounded-lg px-2.5 py-1.5 text-xs appearance-none transition-all duration-200 focus:outline-2 focus:outline-text-l1 pr-8">
                @foreach (var server in Servers)
                {
                    <option value="@server.Key">@server.Value</option>
                }
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-2.5 pointer-events-none">
                <svg class="w-3 h-3 text-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 relative">
        <!-- Account A Login -->
        <div class="bg-[#2E3033] border-2 @(AccountA.IsConnected ? "border-success" : "border-transparent") rounded-lg p-3 transition-all duration-300">
            <div class="flex items-center justify-between mb-2">
                <div class="flex items-center">
                    <div class="w-5 h-5 rounded-lg bg-[#3A3C3F] flex items-center justify-center mr-2 border border-border-strong">
                        <span class="text-xs font-bold text-text-main">A</span>
                    </div>
                    <div>
                        <div class="flex items-center gap-1.5">
                            <h4 class="text-xs font-bold text-text-main font-jakarta">Primary Account</h4>
                            <div class="w-4 h-3 bg-[#3A3C3F] rounded-sm flex items-center justify-center">
                                @((MarkupString)CurrentServerFlag)
                            </div>
                        </div>
                        <p class="text-xs text-text-secondary">Item sender</p>
                    </div>
                </div>
                <div class="flex items-center px-1.5 py-0.5 rounded-md bg-[#1F2023] border border-border-base">
                    <div class="w-1.5 h-1.5 rounded-full @(AccountA.IsConnected ? "bg-success" : "bg-error") mr-1"></div>
                    <span class="text-xs text-text-secondary">@(AccountA.IsConnected ? "Online" : "Offline")</span>
                </div>
            </div>

            <div class="space-y-2">
                <div>
                    <input type="text" placeholder="Username" @bind="AccountA.Username" disabled="@AccountA.IsConnected"
                           class="w-full bg-[#1F2023] border border-border-base rounded-lg px-2.5 py-1.5 text-xs placeholder-text-secondary transition-all duration-200 focus:outline-2 focus:outline-text-l1 @(AccountA.IsConnected ? "opacity-50" : "")" />
                </div>
                <div>
                    <input type="password" placeholder="Password" @bind="AccountA.Password" disabled="@AccountA.IsConnected"
                           class="w-full bg-[#1F2023] border border-border-base rounded-lg px-2.5 py-1.5 text-xs placeholder-text-secondary transition-all duration-200 focus:outline-2 focus:outline-text-l1 @(AccountA.IsConnected ? "opacity-50" : "")" />
                </div>
                <button @onclick="@(async () => await ToggleConnectionAsync("A"))"
                        disabled="@IsAccountConnecting("A")"
                        class="w-full @(IsAccountConnecting("A") ? "bg-[#2E3033] cursor-not-allowed" : AccountA.IsConnected ? "bg-[#5A5C5F] hover:bg-[#6A6C6F]" : "bg-[#3A3C3F] hover:bg-[#4A4C4F]") text-text-main px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center justify-center">
                    @if (IsAccountConnecting("A"))
                    {
                        <svg class="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Connecting...</span>
                    }
                    else
                    {
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            @if (AccountA.IsConnected)
                            {
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            }
                            else
                            {
                                <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" />
                            }
                        </svg>
                        <span>@(AccountA.IsConnected ? "Disconnect" : "Connect")</span>
                    }
                </button>
            </div>
        </div>

        <!-- Exchange Arrow -->
        <div class="hidden sm:flex absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
            <div class="bg-[#2E3033] border border-border-strong rounded-full p-1.5">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-text-main" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </div>
        </div>

        <!-- Account B Login -->
        <div class="bg-[#2E3033] border-2 @(AccountB.IsConnected ? "border-success" : "border-transparent") rounded-lg p-3 transition-all duration-300">
            <div class="flex items-center justify-between mb-2">
                <div class="flex items-center">
                    <div class="w-5 h-5 rounded-lg bg-[#3A3C3F] flex items-center justify-center mr-2 border border-border-strong">
                        <span class="text-xs font-bold text-text-main">B</span>
                    </div>
                    <div>
                        <div class="flex items-center gap-1.5">
                            <h4 class="text-xs font-bold text-text-main font-jakarta">Secondary Account</h4>
                            <div class="w-4 h-3 bg-[#3A3C3F] rounded-sm flex items-center justify-center">
                                @((MarkupString)CurrentServerFlag)
                            </div>
                        </div>
                        <p class="text-xs text-text-secondary">Item receiver</p>
                    </div>
                </div>
                <div class="flex items-center px-1.5 py-0.5 rounded-md bg-[#1F2023] border border-border-base">
                    <div class="w-1.5 h-1.5 rounded-full @(AccountB.IsConnected ? "bg-success" : "bg-error") mr-1"></div>
                    <span class="text-xs text-text-secondary">@(AccountB.IsConnected ? "Online" : "Offline")</span>
                </div>
            </div>

            <div class="space-y-2">
                <div>
                    <input type="text" placeholder="Username" @bind="AccountB.Username" disabled="@AccountB.IsConnected"
                           class="w-full bg-[#1F2023] border border-border-base rounded-lg px-2.5 py-1.5 text-xs placeholder-text-secondary transition-all duration-200 focus:outline-2 focus:outline-text-l1 @(AccountB.IsConnected ? "opacity-50" : "")" />
                </div>
                <div>
                    <input type="password" placeholder="Password" @bind="AccountB.Password" disabled="@AccountB.IsConnected"
                           class="w-full bg-[#1F2023] border border-border-base rounded-lg px-2.5 py-1.5 text-xs placeholder-text-secondary transition-all duration-200 focus:outline-2 focus:outline-text-l1 @(AccountB.IsConnected ? "opacity-50" : "")" />
                </div>
                <button @onclick="@(async () => await ToggleConnectionAsync("B"))"
                        disabled="@IsAccountConnecting("B")"
                        class="w-full @(IsAccountConnecting("B") ? "bg-[#2E3033] cursor-not-allowed" : AccountB.IsConnected ? "bg-[#5A5C5F] hover:bg-[#6A6C6F]" : "bg-[#3A3C3F] hover:bg-[#4A4C4F]") text-text-main px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center justify-center">
                    @if (IsAccountConnecting("B"))
                    {
                        <svg class="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Connecting...</span>
                    }
                    else
                    {
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            @if (AccountB.IsConnected)
                            {
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            }
                            else
                            {
                                <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" />
                            }
                        </svg>
                        <span>@(AccountB.IsConnected ? "Disconnect" : "Connect")</span>
                    }
                </button>
            </div>
        </div>
    </div>
</div>
