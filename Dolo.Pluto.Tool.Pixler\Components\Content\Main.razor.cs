using System.Diagnostics.CodeAnalysis;
using Dolo.Pluto.Shard.Components;
using Dolo.Pluto.Tool.Pixler.Services;
using Dolo.Pluto.Tool.Pixler.Components;
using Microsoft.AspNetCore.Components;

namespace Dolo.Pluto.Tool.Pixler.Components.Content;

public partial class Main : ComponentBase {

    [Inject, AllowNull] public MainService MainServiceInstance { get; set; }

    public InitializationScreen? InitializationScreen;

    // Component references
    public AccountSetup? AccountSetupRef;
    public ItemSelection? ItemSelectionRef;

    // Exchange state
    public bool IsExchangeActive = false;
    public string CurrentExchangeItem = "💍";
    public string CurrentExchangeItemName = "Diamond Ring";
    public int ExchangeProgress = 0;

    public Task StateHasChangedAsync() => InvokeAsync(StateHasChanged);

    protected override Task OnAfterRenderAsync(bool firstRender) {
        if (!firstRender) return Task.CompletedTask;

        MainServiceInstance.Main = this;
        return Task.CompletedTask;
    }

    public async Task CancelExchange()
    {
        IsExchangeActive = false;
        ExchangeProgress = 0;
        StateHasChanged();
        await Task.CompletedTask;
    }
}
