﻿using Dolo.Bot.Pixi.Hub.Global.Enums;
using Dolo.Core.Discord;
using Dolo.Planet.Enums;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("theme")]
    [Description("get a animation by the name or id")]
    public async Task ThemeAsync(SlashCommandContext ctx,
        [Description("theme type")] ThemeType type)
    {
        await ctx.LogAsync($"/msp theme {type}");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync()) return;

        // check if the shard is ready to use 
        var msp = await ctx.IsShardReadyAsync(Server.Germany);
        if (msp is null) return;

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » **Searching themes ..**");

        // get the id of the user if the connection failed return
        var themes = await msp.GetThemesAsync(100, type == ThemeType.Upcoming);
        if (!themes.Success)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Interaction failed with response `{themes.GetStatusCode()}`**");
            return;
        }

        // check if upcoming
        if (type == ThemeType.Upcoming && !themes.HasUpcoming)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Currently there is no upcoming theme.**");
            return;
        }

        // send response
        await ctx.TryEditResponseAsync(await HubEmbed.ThemeAsync(ctx.Guild, themes, type));
    }
}