using System.Net.Http;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.Shard.Services;

public interface IReportService
{
    Task ReportErrorAsync(string errorMessage, string? context = null, Exception? exception = null);
    Task ReportSignalRIssueAsync(string issue, string? connectionDetails = null);
}

public class ReportService(ILogger<ReportService> logger) : IReportService
{
    private readonly HttpClient _httpClient = new() { Timeout = TimeSpan.FromSeconds(10) };
    private const string ReportUrl = "https://msp.cbkdz.eu/api/toolbox/report";

    public async Task ReportErrorAsync(string errorMessage, string? context = null, Exception? exception = null)
    {
        try
        {
            var report = FormatErrorReport(errorMessage, context, exception);
            await SendReportAsync(report);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to report error: {ErrorMessage}", errorMessage);
        }
    }

    public async Task ReportSignalRIssueAsync(string issue, string? connectionDetails = null)
    {
        try
        {
            var report = FormatSignalRReport(issue, connectionDetails);
            await SendReportAsync(report);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to report SignalR issue: {Issue}", issue);
        }
    }

    private string FormatErrorReport(string errorMessage, string? context, Exception? exception)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC");
        var report = new StringBuilder();

        report.AppendLine("🚨 **Pluto Shard Error Report**");
        report.AppendLine($"⏰ **Time:** {timestamp}");
        report.AppendLine($"🖥️ **Component:** Pluto Shard");

        if (!string.IsNullOrEmpty(context))
            report.AppendLine($"📍 **Context:** {context}");

        report.AppendLine($"📝 **Message:**");
        report.AppendLine($"```");
        report.AppendLine(errorMessage);
        report.AppendLine($"```");

        if (exception == null) return report.ToString();

        report.AppendLine($"📋 **Exception Details:**");
        report.AppendLine($"```");
        report.AppendLine($"Type: {exception.GetType().Name}");
        report.AppendLine($"Message: {exception.Message}");
        if (!string.IsNullOrEmpty(exception.StackTrace))
        {
            var stackTrace = exception.StackTrace.Length > 1000
                ? exception.StackTrace[..1000] + "..."
                : exception.StackTrace;
            report.AppendLine($"Stack: {stackTrace}");
        }
        report.AppendLine($"```");

        return report.ToString();
    }

    private string FormatSignalRReport(string issue, string? connectionDetails)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC");
        var report = new StringBuilder();

        report.AppendLine("🔌 **SignalR Connection Issue**");
        report.AppendLine($"⏰ **Time:** {timestamp}");
        report.AppendLine($"🖥️ **Component:** Pluto Shard SignalR");
        report.AppendLine($"📝 **Issue:**");
        report.AppendLine($"```");
        report.AppendLine(issue);
        report.AppendLine($"```");

        if (!string.IsNullOrEmpty(connectionDetails))
        {
            report.AppendLine($"🔗 **Connection Details:**");
            report.AppendLine($"```");
            report.AppendLine(connectionDetails);
            report.AppendLine($"```");
        }

        return report.ToString();
    }

    private async Task SendReportAsync(string report)
    {
        try
        {
            var json = JsonSerializer.Serialize(report);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            logger.LogDebug("Sending error report to {Url}", ReportUrl);

            var response = await _httpClient.PostAsync(ReportUrl, content);

            if (response.IsSuccessStatusCode)
                logger.LogDebug("Error report sent successfully");
            else
                logger.LogWarning("Failed to send error report. Status: {StatusCode}", response.StatusCode);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Exception while sending error report");
        }
    }

    public void Dispose() => _httpClient?.Dispose();
}
