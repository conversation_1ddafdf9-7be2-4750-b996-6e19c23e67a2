﻿namespace Dolo.Bot.Pixi.Hub.Global;

public static class MspTrackingProcessor
{
    public static Task StartAsync() => _ = Task.Factory.StartNew(async () => {
        var timer = new PeriodicTimer(TimeSpan.FromMinutes(30));
        return;
        while (await timer.WaitForNextTickAsync()) {
            if (Hub.IsInitializing || !Hub.IsReady || Hub.MspShard is null)
                continue;

            var shards = Hub.MspShard.GetAll();
            foreach (var msp in shards.Where(a => a.IsLoggedIn)) {
                var dump = await msp.GetHighscoreActorsAsync(a => a.UseCount(5000));
                foreach (var actor in dump)
                    await HubTracking.AddTrackingAsync(actor);
            }
        }
    });
}