Stack trace:
Frame         Function      Args
0007FFFFAAF0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFAAF0, 0007FFFF99F0) msys-2.0.dll+0x1FE8E
0007FFFFAAF0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFADC8) msys-2.0.dll+0x67F9
0007FFFFAAF0  000210046832 (000210286019, 0007FFFFA9A8, 0007FFFFAAF0, 000000000000) msys-2.0.dll+0x6832
0007FFFFAAF0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAAF0  000210068E24 (0007FFFFAB00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFADD0  00021006A225 (0007FFFFAB00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBF1280000 ntdll.dll
7FFBF00D0000 KERNEL32.DLL
7FFBEEC00000 KERNELBASE.dll
7FFBEAF70000 apphelp.dll
7FFBF09C0000 USER32.dll
7FFBEEBD0000 win32u.dll
7FFBF00A0000 GDI32.dll
7FFBEE910000 gdi32full.dll
7FFBEE7A0000 msvcp_win.dll
7FFBEE3A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBF0DD0000 advapi32.dll
7FFBEF510000 msvcrt.dll
7FFBEF770000 sechost.dll
7FFBF0B90000 RPCRT4.dll
7FFBED990000 CRYPTBASE.DLL
7FFBEE580000 bcryptPrimitives.dll
7FFBF0980000 IMM32.DLL
