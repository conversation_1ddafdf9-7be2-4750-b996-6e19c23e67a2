﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Interaction;

public static class Setting
{
    public static async Task HandleSettingAsync(this ComponentInteractionCreatedEventArgs args)
    {
        await args.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.DeferredMessageUpdate);
        await args.HandleWelcomeMessageAsync();
        await args.HandleWelcomeMessageDeleteAsync();
        await args.HandleFakeNotifyAsync();
    }


    private static async Task HandleFakeNotifyAsync(this ComponentInteractionCreatedEventArgs args)
    {
        if (args.Interaction is { Type: DiscordInteractionType.Component, Data.CustomId: "fake-notify-true" or "fake-notify-false" })
        {
            await args.Interaction.TryDeleteOriginalMessageAsync();
            await Mongo.ServerSettings.UpdateAsync(Builders<ServerSettings>.Filter.Empty, Builders<ServerSettings>.Update.Set(a => a.IsFakeNotifyEnabled, args.Interaction.Data.CustomId == "fake-notify-true"));

            var setting = await Mongo.ServerSettings.GetFirstAsync();
            await args.Interaction.TryCreateFollowupMessageAsync(new DiscordFollowupMessageBuilder()
                .AsEphemeral()
                .AddEmbed(new DiscordEmbedBuilder()
                    .WithDescription("Enable or disable the fake notification")
                    .WithColor(DiscordColor.White))
                .AddComponents(new DiscordButtonComponent(DiscordButtonStyle.Primary, "fake-notify-true", "Enable", setting!.IsFakeNotifyEnabled),
                new DiscordButtonComponent(DiscordButtonStyle.Danger, "fake-notify-false", "Disable", !setting.IsFakeNotifyEnabled)));
        }

        if (args.Values.FirstOrDefault() == "fake-notify")
        {
            var setting = await Mongo.ServerSettings.GetFirstAsync();
            await args.Interaction.TryCreateFollowupMessageAsync(new DiscordFollowupMessageBuilder()
                .AsEphemeral()
                .AddEmbed(new DiscordEmbedBuilder()
                    .WithDescription("Enable or disable the fake notification")
                    .WithColor(DiscordColor.White))
                .AddComponents(new DiscordButtonComponent(DiscordButtonStyle.Primary, "fake-notify-true", "Enable", setting!.IsFakeNotifyEnabled),
                new DiscordButtonComponent(DiscordButtonStyle.Danger, "fake-notify-false", "Disable", !setting.IsFakeNotifyEnabled)));
        }
    }

    private static async Task HandleWelcomeMessageDeleteAsync(this ComponentInteractionCreatedEventArgs args)
    {
        if (args.Interaction is { Type: DiscordInteractionType.Component, Data.CustomId: "welcome-message-delete-true" or "welcome-message-delete-false" })
        {
            await args.Interaction.TryDeleteOriginalMessageAsync();
            await Mongo.ServerSettings.UpdateAsync(Builders<ServerSettings>.Filter.Empty, Builders<ServerSettings>.Update.Set(a => a.IsWelcomeMessageDeleteAfterEnabled, args.Interaction.Data.CustomId == "welcome-message-delete-true"));

            var setting = await Mongo.ServerSettings.GetFirstAsync();
            await args.Interaction.TryCreateFollowupMessageAsync(new DiscordFollowupMessageBuilder()
                .AsEphemeral()
                .AddEmbed(new DiscordEmbedBuilder()
                    .WithDescription("Enable or disable the welcome message delete after a certain time")
                    .WithColor(DiscordColor.White))
                .AddComponents(new DiscordButtonComponent(DiscordButtonStyle.Primary, "welcome-message-delete-true", "Enable", setting!.IsWelcomeMessageDeleteAfterEnabled),
                new DiscordButtonComponent(DiscordButtonStyle.Danger, "welcome-message-delete-false", "Disable", !setting.IsWelcomeMessageDeleteAfterEnabled)));
        }

        if (args.Values.FirstOrDefault() == "welcome-message-delete")
        {
            var setting = await Mongo.ServerSettings.GetFirstAsync();
            await args.Interaction.TryCreateFollowupMessageAsync(new DiscordFollowupMessageBuilder()
                .AsEphemeral()
                .AddEmbed(new DiscordEmbedBuilder()
                    .WithDescription("Enable or disable the welcome message delete after a certain time")
                    .WithColor(DiscordColor.White))
                .AddComponents(new DiscordButtonComponent(DiscordButtonStyle.Primary, "welcome-message-delete-true", "Enable", setting!.IsWelcomeMessageDeleteAfterEnabled),
                new DiscordButtonComponent(DiscordButtonStyle.Danger, "welcome-message-delete-false", "Disable", !setting.IsWelcomeMessageDeleteAfterEnabled)));
        }
    }

    private static async Task HandleWelcomeMessageAsync(this ComponentInteractionCreatedEventArgs args)
    {
        if (args.Interaction is { Type: DiscordInteractionType.Component, Data.CustomId: "welcome-message-true" or "welcome-message-false" })
        {
            await args.Interaction.TryDeleteOriginalMessageAsync();
            await Mongo.ServerSettings.UpdateAsync(Builders<ServerSettings>.Filter.Empty, Builders<ServerSettings>.Update.Set(a => a.IsWelcomeMessageEnabled, args.Interaction.Data.CustomId == "welcome-message-true"));

            var setting = await Mongo.ServerSettings.GetFirstAsync();
            await args.Interaction.TryCreateFollowupMessageAsync(new DiscordFollowupMessageBuilder()
                .AsEphemeral()
                .AddEmbed(new DiscordEmbedBuilder()
                    .WithDescription("Enable or disable the welcome message")
                    .WithColor(DiscordColor.White))
                .AddComponents(new DiscordButtonComponent(DiscordButtonStyle.Primary, "welcome-message-true", "Enable", setting!.IsWelcomeMessageEnabled),
                new DiscordButtonComponent(DiscordButtonStyle.Danger, "welcome-message-false", "Disable", !setting.IsWelcomeMessageEnabled)));
        }

        if (args.Values.FirstOrDefault() == "welcome-message")
        {
            var setting = await Mongo.ServerSettings.GetFirstAsync();
            await args.Interaction.TryCreateFollowupMessageAsync(new DiscordFollowupMessageBuilder()
                .AsEphemeral()
                .AddEmbed(new DiscordEmbedBuilder()
                    .WithDescription("Enable or disable the welcome message")
                    .WithColor(DiscordColor.White))
                .AddComponents(new DiscordButtonComponent(DiscordButtonStyle.Primary, "welcome-message-true", "Enable", setting!.IsWelcomeMessageEnabled),
                new DiscordButtonComponent(DiscordButtonStyle.Danger, "welcome-message-false", "Disable", !setting.IsWelcomeMessageEnabled)));
        }
    }
}