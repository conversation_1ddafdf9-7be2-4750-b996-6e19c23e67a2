﻿using Dolo.Core;
using Dolo.Core.Discord;
using Dolo.Core.Http;
using Dolo.Database;
using Dolo.Flash;
using Dolo.Planet.Enums;
using Dolo.Pluto.Shard.Bot.Pixi;
using DSharpPlus.SlashCommands.Attributes;
using System.Runtime.Versioning;
using System.Text;
using System.Text.RegularExpressions;

namespace Dolo.Bot.Pixi.Hub.Global
{
    public partial class Msp
    {
        /// <summary>
        /// Previews a clothing item based on the provided ID.
        /// </summary>
        /// <param name="ctx">The context of the slash command.</param>
        /// <param name="id">The ID of the clothing item to preview.</param>
        [SupportedOSPlatform("windows")]
        [Command("preview")]
        [Description("preview a clothing")]
        [SlashCooldown(2, 30, SlashCooldownBucketType.User)]
        public async Task PreviewAsync(SlashCommandContext ctx,
            [Description("id of cloth")] long id)
        {
            await ctx.LogAsync($"/msp preview {id}");

            // Check if the command can be executed
            if (!await ctx.IsOkayAsync()) return;

            // Check if the shard is ready to use
            var msp = await ctx.IsShardReadyAsync(Server.Germany);
            if (msp is null) return;

            // Try to get the clothing item from the cache
            var cache = await Mongo.PixiCloth.GetOneAsync(a => a.Id == id.ToString());
            if (cache != null)
            {
                await ctx.TryEditResponseAsync(new DiscordWebhookBuilder()
                    .AddEmbed(new DiscordEmbedBuilder()
                        .WithColor(new(await Hub.GetEmbedColorAsync()))
                        .WithTitle($"**Preview of `{cache.Name}`**")
                        .WithImageUrl(cache.SwfUrl)
                        .WithDescription(new StringBuilder()
                            .AppendLine($"{HubEmoji.Astro} **»** [Id](https://a) » **{cache.Id}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Name](https://a) » **{cache.Name}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Category](https://a) » **{cache.Category}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [StarCoins](https://a) » **{cache.Price}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Diamonds](https://a) » **{cache.DiamondPrice}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [VIP](https://a) » {(cache.IsVip ? HubEmoji.Yes : HubEmoji.No)}")
                            .AppendLine($"{HubEmoji.Astro} **»** [Rare](https://a) » {(cache.IsRare ? HubEmoji.Yes : HubEmoji.No)}")
                            .AppendLine($"{HubEmoji.Astro} **»** [ColorSlots](https://a) » **{cache.ColorSlots}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Colors](https://a) ```{cache.Colors}```")
                            .ToString())));
                return;
            }

            await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » **Searching for cloth ..**");

            // Get the clothing item by ID
            var cloth = await msp.LoadClothesByIdsAsync(Convert.ToInt32(id));
            if (!cloth.Success)
            {
                await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Interaction failed with response `{cloth.GetStatusCode()}`**");
                return;
            }

            if (!cloth.Any())
            {
                await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **No cloth found with id `{id}`**");
                return;
            }

            var cl = cloth.First();
            var url = new Uri(cl.SwfUrl!);
            var direct = new Direct().Create();

            try
            {
                await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Processing ..**");

                // Download the SWF file
                var download = await direct.TryDownloadAsync(url);
                if (download is null)
                {
                    await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Failed to process swf `{cl.Name}`**");
                    return;
                }

                // copy the stream to the file
                direct.TryConvertStreamToFile(download, direct.GetFilePath(url));

                await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Processing swf `{cl.Name}` ..**");
                await direct.TryExportAsync(url);

                await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Modifying colors for `{cl.Name}` ..**");
                await direct.TryApplyColorAsync();

                await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Processing `{cl.Name}` ..**");

                // Send the processed image to the channel
                var message = await HubChannel.Swf!.TrySendMessageAsync(a => a.AddFile("preview.png", direct.GetStream()));

                if (!await Http.IsImageAvailableAsync(message?.Attachments[0].Url))
                {
                    await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Failed to process swf `{cl.Name}`**");
                    return;
                }

                var color = new Regex("\\b0x[0-9A-Fa-f]{6}\\b").Matches(cl.ColorScheme ?? "").Select(a => a.Value).ToArray();
                await ctx.TryEditResponseAsync(new DiscordWebhookBuilder()
                    .AddEmbed(new DiscordEmbedBuilder()
                        .WithColor(new(await Hub.GetEmbedColorAsync()))
                        .WithTitle($"**Preview of `{cl.Name}`**")
                        .WithImageUrl(message?.Attachments[0].Url)
                        .WithDescription(new StringBuilder()
                            .AppendLine($"{HubEmoji.Astro} **»** [Id](https://a) » **{cl.Id}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Name](https://a) » **{cl.Name}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Category](https://a) » **{cl.Category?.Name}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [StarCoins](https://a) » **{cl.Price}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Diamonds](https://a) » **{cl.DiamondsPrice}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [VIP](https://a) » {(cl.IsVip ? HubEmoji.Yes : HubEmoji.No)}")
                            .AppendLine($"{HubEmoji.Astro} **»** [Rare](https://a) » {(cl.ShopId == -100 ? HubEmoji.Yes : HubEmoji.No)}")
                            .AppendLine($"{HubEmoji.Astro} **»** [ColorSlots](https://a) » **{color.Length}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Colors](https://a) ```{(color.Length == 0 ? "no color" : string.Join(",", color))}```")
                            .ToString())));

                // Add the clothing item to the cache
                await Mongo.PixiCloth.AddAsync(new PixiCloth
                {
                    Name = cl.Name,
                    Id = cl.Id.ToString(),
                    Category = cl.Category?.Name,
                    Price = cl.Price.ToString(),
                    DiamondPrice = cl.DiamondsPrice.ToString(),
                    SwfUrl = message.Attachments[0].Url,
                    Colors = color.Length == 0 ? "no color" : string.Join(",", color),
                    ColorSlots = color.Length,
                    IsVip = cl.IsVip,
                    IsRare = cl.ShopId == -100
                });
                direct.Delete();
            }
            catch (Exception ex)
            {
                await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Failed to process ..**");
                direct.Delete();
                Console.WriteLine(ex.ToJson());
            }
        }
    }
}