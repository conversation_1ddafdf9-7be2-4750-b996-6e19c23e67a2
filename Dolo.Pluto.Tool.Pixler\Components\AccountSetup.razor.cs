using Microsoft.AspNetCore.Components;

namespace Dolo.Pluto.Tool.Pixler.Components;

public partial class AccountSetup : ComponentBase
{
    public class AccountData
    {
        public string Username { get; set; } = "";
        public string Password { get; set; } = "";
        public bool IsConnected { get; set; } = false;
    }

    public AccountData AccountA { get; set; } = new();
    public AccountData AccountB { get; set; } = new();
    
    public string SelectedServer { get; set; } = "us";

    public Dictionary<string, string> Servers { get; set; } = new()
    {
        { "us", "🇺🇸 United States" },
        { "uk", "🇬🇧 United Kingdom" },
        { "de", "🇩🇪 Germany" },
        { "fr", "🇫🇷 France" },
        { "es", "🇪🇸 Spain" },
        { "se", "🇸🇪 Sweden" },
        { "nl", "🇳🇱 Netherlands" },
        { "au", "🇦🇺 Australia" }
    };

    public Dictionary<string, string> ServerFlags { get; set; } = new()
    {
        { "us", """<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="16" fill="#B22234"/><rect width="24" height="1.23" y="1.23" fill="white"/><rect width="24" height="1.23" y="3.69" fill="white"/><rect width="24" height="1.23" y="6.15" fill="white"/><rect width="24" height="1.23" y="8.62" fill="white"/><rect width="24" height="1.23" y="11.08" fill="white"/><rect width="24" height="1.23" y="13.54" fill="white"/><rect width="9.6" height="8.62" fill="#3C3B6E"/></svg>""" },
        { "uk", """<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="16" fill="#012169"/><path d="M0 0l24 16M24 0L0 16" stroke="white" stroke-width="1.6"/><path d="M0 0l24 16M24 0L0 16" stroke="#C8102E" stroke-width="1"/></svg>""" },
        { "de", """<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="5.33" fill="black"/><rect width="24" height="5.33" y="5.33" fill="#DD0000"/><rect width="24" height="5.33" y="10.67" fill="#FFCE00"/></svg>""" },
        { "fr", """<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="8" height="16" fill="#002395"/><rect width="8" height="16" x="8" fill="white"/><rect width="8" height="16" x="16" fill="#ED2939"/></svg>""" },
        { "es", """<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="4" fill="#AA151B"/><rect width="24" height="8" y="4" fill="#F1BF00"/><rect width="24" height="4" y="12" fill="#AA151B"/></svg>""" },
        { "se", """<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="16" fill="#006AA7"/><rect width="24" height="2" y="7" fill="#FECC00"/><rect width="2" height="16" x="7" fill="#FECC00"/></svg>""" },
        { "nl", "🇳🇱" },
        { "au", "🇦🇺" }
    };

    public string CurrentServerFlag => ServerFlags.TryGetValue(SelectedServer, out var flag) ? flag : ServerFlags["us"];

    public bool IsConnecting { get; set; } = false;
    public string ConnectingAccount { get; set; } = "";

    private async Task ToggleConnectionAsync(string account)
    {
        var accountData = account == "A" ? AccountA : AccountB;

        if (accountData.IsConnected)
        {
            // Disconnect
            accountData.IsConnected = false;
            StateHasChanged();
        }
        else
        {
            // Validate inputs
            if (string.IsNullOrWhiteSpace(accountData.Username) || string.IsNullOrWhiteSpace(accountData.Password))
            {
                // Show validation message (could be replaced with toast notification)
                await ShowValidationMessage("Please enter both username and password");
                return;
            }

            // Set connecting state
            IsConnecting = true;
            ConnectingAccount = account;
            StateHasChanged();

            try
            {
                // Simulate connection delay
                await Task.Delay(2000);

                // Connect
                accountData.IsConnected = true;
            }
            finally
            {
                // Reset connecting state
                IsConnecting = false;
                ConnectingAccount = "";
                StateHasChanged();
            }
        }
    }

    private async Task ShowValidationMessage(string message)
    {
        // Simple alert simulation - in real app this would be a toast or modal
        // For now, we'll just log it (could be enhanced with IJSRuntime for actual alerts)
        Console.WriteLine($"Validation Error: {message}");
        await Task.CompletedTask;
    }

    public bool IsAccountConnecting(string account) => IsConnecting && ConnectingAccount == account;
}
