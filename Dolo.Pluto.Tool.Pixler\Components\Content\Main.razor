@page "/"
@using Dolo.Pluto.Shard.Components
@using Dolo.Pluto.Tool.Pixler.Components

<div class="flex flex-col h-screen">
    <Header />

    <div class="flex-1 flex overflow-hidden">
        <div class="flex-1 flex flex-col bg-[#1F2023] overflow-hidden">
            <div class="flex-1 overflow-y-auto">
                <div class="w-full max-w-4xl mx-auto p-3">
                    <AccountSetup @ref="AccountSetupRef" />
                    <ItemSelection @ref="ItemSelectionRef" />
                </div>
            </div>
        </div>
    </div>

    <ExchangePreview IsActive="@IsExchangeActive"
                     CurrentItem="@CurrentExchangeItem"
                     CurrentItemName="@CurrentExchangeItemName"
                     Progress="@ExchangeProgress"
                     OnCancel="@CancelExchange" />
</div>

