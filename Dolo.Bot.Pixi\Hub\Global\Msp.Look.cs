﻿using Dolo.Core.Discord;
using Dolo.Planet;
using Dolo.Planet.Enums;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("look")]

[Description("get the look from a user")]
    public async Task LookAsync(SlashCommandContext ctx,
        [Description("player server")] Server server,
        [Description("player username")] string username)
    {
        await ctx.LogAsync($"/msp look {username} {server}");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync()) return;

        // check if the shard is ready to use 
        var msp = await ctx.IsShardReadyAsync(server);
        if (msp is null) return;

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **Searching for `{username}` ..**");

        // get the id of the user if the connection failed return
        var user = await msp.GetActorAsync(username);
        if (!user.Success)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Interaction failed with response `{user.GetStatusCode()}`**");
            return;
        }

        // return if the user is not available
        if (!user.IsAvailable)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » {MspClientUtil.GetServerDiscordFlag(server)} » **User `{username}` is not available**");
            return;
        }

        // send response
        await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » {user.Username} (`{user.Server}`)");
        await ctx.Channel.TrySendMessageAsync(user.Avatar.BodyUrl);
    }
}