﻿using Dolo.Core.Discord;
using Dolo.Planet;
using Dolo.Planet.Enums;
using DSharpPlus.SlashCommands.Attributes;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("name")]
    [Description("get name of a user with the id")]
    [SlashCooldown(2, 120, SlashCooldownBucketType.User)]
    public async Task NameAsync(SlashCommandContext ctx,
        [Description("player server")] Server server,
        [Description("player id")] long Id)
    {
        // log command
        await ctx.LogAsync($"/msp name {Id} {server}");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync(true)) return;

        // check if the shard is ready to use 
        var msp = await ctx.IsShardReadyAsync(server);
        if (msp is null) return;

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **Searching for id `{Id}` ..**");

        // get the id of the user if the connection failed return
        var user = await msp.GetActorNameAsync(Convert.ToInt32(Id));
        if (!user.Success)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Interaction failed with response `{user.GetStatusCode()}`**");
            return;
        }

        // return if the user is not available
        if (!user.IsAvailable)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » User with id `{Id}` from `{server}` is not available.");
            return;
        }

        // send response
        await ctx.TryEditResponseAsync($"-# {MspClientUtil.GetServerDiscordFlag(server)} » **{user.Username}** » `{user.Id}`");
    }
}