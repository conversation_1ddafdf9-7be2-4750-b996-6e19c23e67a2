﻿using Dolo.Database;
using Dolo.Pluto.Shard;
namespace Dolo.Bot.Apple.Hub.Voice;

public partial class Voice
{
    [Command("unban")]
    [Description("unban a user out of the voice channel")]
    public async Task VoiceUnbanAsync(SlashCommandContext ctx, [Description("the user which should be unbanned")] DiscordUser user)
    {
        if (Hub.Guild is null)
            return;

        // defer the message
        await ctx.Interaction.DeferAsync();

        // print error if the topic is not the voice system
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryEditResponseAsync("This command can only be used in voice channels.");
            return;
        }

        // check if the user is a member of the server
        if (!Hub.Guild.TryGetMember(user.Id, out _))
        {
            await ctx.TryEditResponseAsync($"{user.Username} is not a server member.");
            return;
        }

        // get the channel database entry
        var usr = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (usr is null)
            return;

        // try to get the channel 
        var channel = Hub.Guild.TryGetChannel(usr.Channel);
        if (channel is null)
            return;

        // check if the user is not banned
        if (!usr.Banned.Contains(user.Id))
        {
            await ctx.TryEditResponseAsync("The user is not banned.");
            return;
        }

        // check if the user is a moderator
        if (usr.Moderator.Contains(user.Id))
        {
            await ctx.TryEditResponseAsync("The user is an moderator and cannot be banned.");
            return;
        }

        // check if the user is allowed to perform the command
        if (usr.Owner != ctx.User.Id && !usr.Moderator.Contains(ctx.User.Id))
        {
            await ctx.TryEditResponseAsync("You are not a channel moderator.");
            return;
        }

        // add banned user into database
        usr.Banned.Remove(user.Id);

        // update the database entry with builders
        await Mongo.Voice.UpdateAsync(Builders<VoiceUser>.Filter.Eq(a => a.TextChannel, ctx.Channel.Id), Builders<VoiceUser>.Update.Set(a => a.Banned, usr.Banned));

        // send the message
        await ctx.TryEditResponseAsync($"{user.Mention} has been unbanned from the voice channel.");
    }
}