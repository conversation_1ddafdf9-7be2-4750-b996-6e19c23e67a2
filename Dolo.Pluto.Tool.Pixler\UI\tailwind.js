tailwind.config = {
    theme: {
        extend: {
            colors: {
                // Text colors
                text: {
                    'main': 'hsl(210, 40%, 96%)', // #F1F5F9 in HSL
                    'secondary': 'hsl(214, 20%, 69%)', // #94A3B8 in HSL
                },

                // Background colors
                bg: {
                    'base': 'hsl(220, 6%, 13%)', // #1A1D21 in HSL
                    'secondary': 'hsl(220, 6%, 10%)', // #1F2328 in HSL
                    'surface': 'hsl(220, 6%, 15%)', // #282C34 in HSL
                    'surface-hover': 'hsl(220, 5%, 19%)', // #343A46 in HSL
                    'avatar': 'hsl(220, 4%, 24%)', // #2A2E36 in HSL
                    'badge': 'hsl(220, 8%, 20%)', // Improved badge background for better visibility
                    'hover': 'hsl(220, 5%, 19%)', // For hover states
                },

                // Label colors - improved for dark mode
                label: {
                    'text': 'hsl(214, 25%, 80%)',     // Lighter than before for better visibility
                    'focus': 'hsl(210, 40%, 92%)',    // Brighter for focus states
                    'bg': 'hsl(220, 7%, 20%)',        // Slightly lighter background
                    'border': 'hsl(220, 10%, 25%)',   // Slightly lighter border
                    'muted': 'hsl(214, 15%, 65%)',    // For secondary label text
                    'hover': 'hsl(214, 25%, 85%)',    // For hover states
                },
                
                // Span colors - new section for consistency
                span: {
                    'default': 'hsl(210, 40%, 96%)',  // Same as text-main
                    'muted': 'hsl(214, 20%, 69%)',    // Same as text-secondary
                    'highlight': 'hsl(186, 42%, 70%)', // Lighter version of primary for emphasis
                    'subtle': 'hsl(214, 15%, 60%)',   // More subtle than muted
                    'hover': 'hsl(210, 40%, 100%)',   // For hover states, pure white
                },

                // Border colors
                border: {
                    'base': 'hsl(0, 0%, 14.9%)',
                    'l1': 'hsl(0, 0%, 99%, 0.06)',
                    'l2': 'hsl(0, 0%, 99%, 0.10)',
                    'l3': 'hsl(0, 0%, 99%, 0.20)',
                    'strong': 'hsl(222, 0%, 86%, 0.04)', // card-border
                    'focus': 'hsl(186, 42%, 55%, 0.35)', // #56B6C2 with alpha
                },

                // Functional colors
                'primary': 'hsl(186, 42%, 55%)', // #56B6C2 in HSL
                'primary-dark': 'hsl(186, 39%, 44%)', // #43939D in HSL
                'success': 'hsl(92, 28%, 65%)', // #98C379 in HSL
                'warning': 'hsl(42, 66%, 70%)', // #E5C07B in HSL
                'error': 'hsl(355, 65%, 65%)', // #E06C75 in HSL
                'info': 'hsl(186, 42%, 55%)', // #56B6C2 in HSL

                // Progress indicator colors
                'progress-bg': 'hsl(210, 12%, 10%)', // #15171B in HSL
                'progress-fill': 'hsl(186, 42%, 55%)', // #56B6C2 in HSL

                // Account states
                'account-active': 'hsl(220, 4%, 18%)', // Darker version
                'account-active-hover': 'hsl(220, 4%, 18%)', // Same as active (no hover effect)

                // Badge styles
                'badge': {
                    'bg': 'hsl(220, 8%, 20%)',
                    'text': 'hsl(210, 40%, 88%)',
                    'border': 'hsl(220, 8%, 25%)',
                },

                // Ring coloring
                'ring': {
                    'focus-accent': 'hsl(186, 42%, 55%)', // For focus rings
                },
            },

            fontFamily: {
                'inter': ['Inter', 'sans-serif'],
                'signika': ['Signika', 'sans-serif'],
                'jakarta': ['Plus Jakarta Sans', 'sans-serif'],
            },

            borderRadius: {
                'sm': '0.25rem', // 4px
                'md': '0.5rem',  // 8px
                'lg': '0.75rem',  // 12px - reduced from 1.5rem (24px) to be more subtlements
                '2xl': '1rem',   // 16px
            },

            borderWidth: {
                '0': '0px',      // No-border style
                '1': '1px',      // Thin borders
                '2': '2px',      // Medium borders
            },

            animation: {
                'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                'spin-slow': 'spin 3s linear infinite',
                'bounce-slow': 'bounce 2s infinite',
            },

            boxShadow: {
                'button': 'none',
                'input': 'none',
                'card': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            },

            ringWidth: {
                'DEFAULT': '2px',
                '1': '1px',
                '2': '2px',
                '4': '4px',
            },

            ringColor: {
                'DEFAULT': 'hsl(186, 42%, 55%, 0.5)', // #56B6C2 with 50% alpha
                'focus': 'hsl(186, 42%, 55%, 0.5)',
                'error': 'hsl(355, 65%, 65%, 0.5)', // #E06C75 with 50% alpha
            },

            ringOffsetWidth: {
                'DEFAULT': '0px',
                '1': '1px',
                '2': '2px',
            },

            outline: {
                'focus': '2px solid hsl(186, 42%, 55%, 0.5)', // #56B6C2 with 50% alpha
                'none': 'none',
            },

            transitionProperty: {
                'common': 'color, background-color, border-color, text-decoration-color, fill, stroke',
                'border': 'border-color, border-width',
                'ring': 'box-shadow, outline',
                'all': 'all',
            },

            transitionDuration: {
                '150': '150ms',
                '200': '200ms',
                '300': '300ms',
            },

            transitionTimingFunction: {
                'ease': 'ease',
                'ease-in': 'cubic-bezier(0.4, 0, 1, 1)',
                'ease-out': 'cubic-bezier(0, 0, 0.2, 1)',
                'ease-in-out': 'cubic-bezier(0.4, 0, 0.2, 1)',
            },
        },
    },
    plugins: [],
};