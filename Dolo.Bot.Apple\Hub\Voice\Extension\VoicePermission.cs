﻿namespace Dolo.Bot.Apple.Hub.Voice.Extension;

public static class VoicePermission
{
    public static async Task<bool> IsVoiceChannelAsync(this SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent == HubChannel.VoiceTopic)
            return true;

        await ctx.TryEditResponseAsync("This command can only be used in voice channels.");
        return false;
    }

    public static async Task<bool> IsMemberInVoiceChannelAsync(this SlashCommandContext ctx, DiscordMember member)
    {
        if (member.VoiceState?.Channel != null)
            return true;

        await ctx.TryEditResponseAsync("The user is not in a voice channel.");
        return false;
    }

    public static async Task<bool> IsMemberAdminAsync(this SlashCommandContext ctx, DiscordMember member)
    {
        if (!member.Roles.ContainsMany(HubRoles.Admin, HubRoles.Energy))
            return false;

        await ctx.TryEditResponseAsync("You can not use this command on a staff member.");
        return false;
    }
}