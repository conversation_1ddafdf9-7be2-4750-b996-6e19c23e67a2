namespace Dolo.Pluto.Shard.Services.Initialization;

public interface IInitializationStateManager
{
    bool IsHidden { get; set; }
    string StatusTitle { get; set; }
    string StatusBadgeText { get; set; }
    string OperationDetail { get; set; }
    string MessageArea { get; set; }
    bool IsVerifying { get; set; }
    bool ShowInitializationContent { get; set; }
    bool ShowMaintenanceContent { get; set; }
    bool ShowUpdateContent { get; set; }
    bool ShowLicenseContent { get; set; }
    bool ShowRetryButton { get; set; }
    bool ShowCloseButton { get; set; }

    void ShowInitializing(string toolName);
    void ShowMaintenanceState(string message);
    void ShowUpdateState(string message);
    void ShowLicenseState(string message);
    void ShowErrorState(string message);
    void Hide();
    void Show();

    event Action? OnStateChanged;
}
