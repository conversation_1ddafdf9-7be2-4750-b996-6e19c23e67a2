﻿namespace Dolo.Bot.Apple.Hub.Mod;

public class Setting 
{
    [RequirePermissions(DiscordPermission.BanMembers)]
    [Command("settings")]

[Description("shows the settings of the server")]
    public async Task ShowAsync(SlashCommandContext ctx)
    {
        await ctx.Interaction.DeferAsync(true);
        await ctx.TryEditResponseAsync(new DiscordWebhookBuilder()
            .AddComponents(new DiscordSelectComponent("select-settings", "Select a setting", new List<DiscordSelectComponentOption>
            {
                new("Welcome Message", "welcome-message", "Enable or disable the welcome message"),
                new("Welcome Message Delete", "welcome-message-delete", "Enable or disable the welcome message to delete after specified time"),
                new("Fake Notify", "fake-notify", "Send fake notifications to the server")
            })));
    }
}