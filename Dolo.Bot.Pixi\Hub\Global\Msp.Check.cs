﻿using Dolo.Core;
using Dolo.Core.Discord;
using Dolo.Planet;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("check")]

    [Description("check the connections to every msp server")]
    public async Task CheckAsync(SlashCommandContext ctx)
    {
        await ctx.LogAsync("/msp check");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync()) return;

        // check if shard is null
        if (Hub.MspShard is null)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Please use `/msp init` to initialize.**");
            return;
        }

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » **Checking server connections ..**");

        // init a string builder
        var builder = new StringBuffer();

        // create a list of tasks
        var tasks = new List<Task>();

        // loop through all shards
        Hub.MspShard.GetAll().ToList().ForEach(shard => tasks.Add(Task.Run(async () =>
            {
                // check if user logged in
                if (!shard.User.LoggedIn)
                {
                    await builder.AppendLineAsync($"{MspClientUtil.GetServerDiscordFlag(shard.User.Server)} » {HubEmoji.No} » **FAILED** `{shard.User.StatusCode}`");
                    return;
                }


                // get piggy bank to check server connection
                var stats = await shard.GetActorPictures(shard.User.Actor.Id, 10);

                // if worked print yes emote otherwise no
                await builder.AppendLineAsync(stats.Success ? $"{MspClientUtil.GetServerDiscordFlag(shard.User.Server)} » {HubEmoji.Yes} » **READY** `{shard.User.StatusCode}`"
                                                  : $"{MspClientUtil.GetServerDiscordFlag(shard.User.Server)} » {HubEmoji.No} » **FAILED** `{stats.GetStatusCode()}`");
            })));

        // wait for all tasks to complete
        await Task.WhenAll(tasks);

        // send response
        await ctx.TryEditResponseAsync(await HubEmbed.CheckAsync(ctx.Guild!, builder.ToBuilder()));
    }
}