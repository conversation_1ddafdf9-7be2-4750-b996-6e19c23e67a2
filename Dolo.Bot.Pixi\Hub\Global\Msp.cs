﻿using Dolo.Core.Discord;
namespace Dolo.Bot.Pixi.Hub.Global;

[Command("msp")]
[Description("Group of moviestarplanet commands")]
public partial class Msp
{
    [Command("help")]
    [Description("list of all available commands")]
    public async Task HelpAsync(SlashCommandContext ctx)
    {
        await ctx.LogAsync("/msp help");
        await ctx.TryDeferAsync();
        await ctx.TryEditResponseAsync(await HubEmbed.HelpAsync(ctx.Guild));
    }
}