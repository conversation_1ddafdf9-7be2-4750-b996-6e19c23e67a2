﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Handler;

public static class GuildMemberRemoved
{
    public static async Task InvokeAsync(this GuildMemberRemovedEventArgs e)
    {
        // we remove the member from the cache
        Hub.MemberSearchSystem.RemoveMember(e.Member.Id);

        // get the member from the database
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == e.Member.Id);

        // remove the welcome message if it exist
        if (member?.Welcome != null && HubChannel.Chat != null && member.Welcome.HasWelcome)
            await HubChannel.Chat.TryDeleteMessageAsync(member.Welcome.Id);
    }
}