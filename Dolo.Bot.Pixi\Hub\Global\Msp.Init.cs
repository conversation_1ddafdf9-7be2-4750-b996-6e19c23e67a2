﻿using Dolo.Core.Discord;
using Dolo.Nebula;
using Dolo.Nebula.Enum;
using Dolo.Planet;
using Dolo.Planet.Enums;
using Server = Dolo.Planet.Enums.Server;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("init")]

    [Description("Initialize the connection to the msp server")]
    public async Task InitAsync(SlashCommandContext ctx)
    {
        await ctx.LogAsync("/msp init");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync()) return;

        // check if the initializing is running
        if (Hub.IsInitializing)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Initializing already in progress please wait ..**");
            return;
        }

        if (Hub.MspShard?.Worked == 16)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **You can already use any server.**");
            return;
        }

        if (Hub.MspShard?.Failed > 0)
        {
            foreach (var failed in Hub.MspShard.GetFailed())
                await failed.LoginAsync();

            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Resync failed servers completed. `/msp help`**");
            return;
        }

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » **Initializing started ...**");

        // set the initialize to true
        Hub.IsInitializing = true;

        // get all servers and cast it
        var password = "DuFotze123";
        var servers = Enum.GetValues(typeof(Server))
            .Cast<Server>();

        // create the shard and initialization
        Hub.MspShard = new(servers.Select(server => new MspConfig()
        .SetUsername("winUI")
        .SetPassword(password)
        .SetServer(server)
        .UseLogger(a => a.AddConsole().SetMinimumLevel(LogLevel.Debug))).ToArray());

        Hub.MspShard.SetConfigUsername(Server.UnitedStates, "winUSA");
        Hub.MspShard.SetConfigUsername(Server.Denmark, "winUI2");
        Hub.MspShard.SetConfigUsername(Server.Poland, "windowsui");
        Hub.MspShard.SetConfigUsername(Server.Netherlands, "macui");
        Hub.MspShard.SetConfigUsername(Server.Canada, "windowsgame");
        Hub.MspShard.SetConfigUsername(Server.UnitedStates, "fortigame");
        Hub.MspShard.SetConfigUsername(Server.NewZealand, "zealandgame");
        Hub.MspShard.SetConfigUsername(Server.Sweden, "swedishgirlo");
        Hub.MspShard.SetConfigUsername(Server.Finland, "sumio");
        Hub.MspShard.SetConfigUsername(Server.Norway, "queeninorge");
        Hub.MspShard.SetConfigUsername(Server.Spain, "spaniona");
        Hub.MspShard.SetConfigUsername(Server.Ireland, "magaritairland");
        Hub.MspShard.SetConfigPassword(Server.Germany, "DuHuan123");
        await Hub.MspShard.LoginAsync();

        Hub.IsInitializing = false;
        Hub.IsReady = Hub.MspShard.GetWorked().Any();

        if (!Hub.IsReady)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Please wait a bit and try again**");
            return;
        }

        if (Hub.MspShard.GetAll().Any(a => a.User.StatusCode == LoginStatusCode.SessionLockWait))
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » {Hub.MspShard.GetWorked().First().User.StatusCode}");
            return;
        }

        foreach (var worked in Hub.MspShard.GetWorked()) {
            var nebula = new NebulaClient(config => {
                config.SetClient(worked)
                    .SetGame(GameType.MovieStarPlanet);
            });

            var init = await nebula.TryInitializeAsync();
            if (!init) continue;

            Hub.NebulaClients.Add(worked.User.Server, nebula);
        }

        // resync system
        _ = Task.Run(async () =>
        {
            if (!Hub.MspShard.GetWorked().Any()) return;

            var timer = new PeriodicTimer(new(2, 0, 0, 0));
            while (await timer.WaitForNextTickAsync())
                await Hub.MspShard.ReloginAsync();
        });

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Initializing completed `{Hub.MspShard.Worked}/16` Worked**");
    }
}