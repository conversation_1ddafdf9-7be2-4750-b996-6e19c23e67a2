using Microsoft.AspNetCore.Components;

namespace Dolo.Pluto.Tool.Pixler.Components;

public partial class ItemSelection : ComponentBase, IDisposable
{
    public class GameItem
    {
        public string Name { get; set; } = "";
        public string Icon { get; set; } = "";
    }

    public List<GameItem> Items { get; set; } = new()
    {
        new() { Name = "Diamond Ring", Icon = "💍" },
        new() { Name = "Gold Necklace", Icon = "📿" },
        new() { Name = "Ruby Earrings", Icon = "💎" },
        new() { Name = "Silver Bracelet", Icon = "🔗" },
        new() { Name = "Emerald Pendant", Icon = "🟢" },
        new() { Name = "Crystal Tiara", Icon = "👑" }
    };

    public GameItem? SelectedItem { get; set; }

    protected override void OnInitialized()
    {
        // Set Diamond Ring as default selected item to match HTML
        SelectedItem = Items.FirstOrDefault(i => i.Name == "Diamond Ring");
    }

    private void LoadItems()
    {
        // Simulate loading items - in real implementation this would load from game
        StateHasChanged();
    }

    private void SelectItem(GameItem item)
    {
        SelectedItem = item;
        StateHasChanged();
    }

    public bool IsExchangeInProgress { get; set; } = false;
    public string ExchangeStatus { get; set; } = "";
    public int ExchangeProgress { get; set; } = 0;



    private async Task StartExchange()
    {
        if (SelectedItem == null || IsExchangeInProgress) return;

        IsExchangeInProgress = true;
        ExchangeStatus = "Initializing exchange...";
        ExchangeProgress = 0;
        StateHasChanged();

        // Simulate exchange progress
        await SimulateExchangeProgress();
    }

    private async Task SimulateExchangeProgress()
    {
        var stages = new[]
        {
            ("Connecting to server...", 10),
            ("Validating accounts...", 25),
            ("Preparing item transfer...", 40),
            ("Transferring item...", 70),
            ("Confirming receipt...", 90),
            ("Exchange completed!", 100)
        };

        foreach (var (status, progress) in stages)
        {
            ExchangeStatus = status;
            ExchangeProgress = progress;
            StateHasChanged();

            // Simulate processing time
            await Task.Delay(1500);
        }

        // Reset after completion
        await Task.Delay(2000);
        IsExchangeInProgress = false;
        ExchangeStatus = "";
        ExchangeProgress = 0;
        StateHasChanged();
    }

    public void Dispose()
    {
        // No resources to dispose currently
    }
}
