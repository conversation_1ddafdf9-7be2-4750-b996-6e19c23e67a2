using Microsoft.AspNetCore.Components;

namespace Dolo.Pluto.Tool.Pixler.Components;

public partial class ExchangePreview : ComponentBase, IDisposable
{
    [Parameter] public bool IsActive { get; set; } = false;
    [Parameter] public string CurrentItem { get; set; } = "💍";
    [Parameter] public string CurrentItemName { get; set; } = "Diamond Ring";
    [Parameter] public string CurrentStatus { get; set; } = "Ready to exchange";
    [Parameter] public int Progress { get; set; } = 0;
    [Parameter] public EventCallback OnCancel { get; set; }

    public bool IsExpanded { get; set; } = false;
    public bool IsPaused { get; set; } = false;
    public bool ShowDataTransfer { get; set; } = false;
    public int DataTransferPosition { get; set; } = 0;

    public string AccountAStatus { get; set; } = "Ready";
    public string AccountBStatus { get; set; } = "Ready";

    private Timer? _dataTransferTimer;
    private Timer? _statusUpdateTimer;

    protected override void OnInitialized()
    {
        // Start data transfer animation when active
        if (IsActive)
        {
            StartDataTransferAnimation();
            StartStatusUpdates();
        }
    }

    protected override void OnParametersSet()
    {
        if (IsActive && !ShowDataTransfer)
        {
            StartDataTransferAnimation();
            StartStatusUpdates();
        }
        else if (!IsActive)
        {
            StopAnimations();
        }
    }

    private void ToggleExpanded()
    {
        IsExpanded = !IsExpanded;
        StateHasChanged();
    }

    private void StartDataTransferAnimation()
    {
        ShowDataTransfer = true;
        _dataTransferTimer = new Timer(UpdateDataTransfer, null, 0, 100);
    }

    private void UpdateDataTransfer(object? state)
    {
        if (!ShowDataTransfer || IsPaused) return;

        DataTransferPosition = (DataTransferPosition + 2) % 100;
        InvokeAsync(StateHasChanged);
    }

    private void StartStatusUpdates()
    {
        _statusUpdateTimer = new Timer(UpdateAccountStatus, null, 0, 2000);
    }

    private void UpdateAccountStatus(object? state)
    {
        if (!IsActive || IsPaused) return;

        // Simulate status changes based on progress
        if (Progress < 25)
        {
            AccountAStatus = "Preparing";
            AccountBStatus = "Waiting";
        }
        else if (Progress < 50)
        {
            AccountAStatus = "Sending";
            AccountBStatus = "Waiting";
        }
        else if (Progress < 75)
        {
            AccountAStatus = "Sending";
            AccountBStatus = "Receiving";
        }
        else if (Progress < 100)
        {
            AccountAStatus = "Confirming";
            AccountBStatus = "Confirming";
        }
        else
        {
            AccountAStatus = "Complete";
            AccountBStatus = "Complete";
        }

        InvokeAsync(StateHasChanged);
    }

    private void PauseExchange()
    {
        IsPaused = !IsPaused;
        StateHasChanged();
    }

    private async Task CancelExchange()
    {
        StopAnimations();
        await OnCancel.InvokeAsync();
    }

    private void StopAnimations()
    {
        ShowDataTransfer = false;
        _dataTransferTimer?.Dispose();
        _statusUpdateTimer?.Dispose();
        _dataTransferTimer = null;
        _statusUpdateTimer = null;
    }

    public void Dispose()
    {
        StopAnimations();
    }
}
