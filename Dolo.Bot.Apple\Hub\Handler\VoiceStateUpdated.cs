﻿namespace Dolo.Bot.Apple.Hub.Handler;

public static class VoiceStateUpdated
{
    public static async Task InvokeAsync(this VoiceStateUpdatedEventArgs e)
    {
        if (Hub.Guild is null
            || HubChannel.Voice is null
            || HubChannel.VoiceTopic is null
            || e.Channel        != null && e.Channel.Parent       != HubChannel.VoiceTopic
            || e.After?.Channel != null && e.After.Channel.Parent != HubChannel.VoiceTopic)
            return;

        // invoked when the user joined the create channel
        if (e.Channel != null && e.Channel.Id == HubChannel.Voice.Id)
            await UserJoinedCreateChannel.InvokeAsync(new(e.Channel, e.User, e.Guild));

        // invoked when the user left the voice channel and it was empty
        if (e.Before?.Channel != null && e.Before.Channel.Id != HubChannel.Voice.Id && !e.Before.Channel.Users.Any())
            await UserLeftVoiceEmpty.InvokeAsync(new(e.Before.Channel, e.User, e.Guild));

        // invoked when the user joined the voice channel
        if (e.After?.Channel != null && e.After.Channel.Id != HubChannel.Voice.Id)
            await UserJoinedVoice.InvokeAsync(new(e.Channel, e.User, e.Guild));

        // invoked when the user left the voice channel
        if (e.Before                     != null
            && e.Before.IsSelfMuted      == e.After?.IsSelfMuted
            && e.Before.IsSelfDeafened   == e.After?.IsSelfDeafened
            && e.Before.IsSelfStream     == e.After?.IsSelfStream
            && e.Before.IsSelfVideo      == e.After?.IsSelfVideo
            && e.Before.IsServerDeafened == e.After?.IsServerDeafened
            && e.Before.IsServerMuted    == e.After?.IsServerMuted
            && e.Before.IsSuppressed     == e.After?.IsSuppressed)
            await UserLeftVoice.InvokeAsync(new(e.Before.Channel, e.User, e.Guild));
    }
}