﻿global using DSharpPlus;
global using Dolo.Bot.Log.Hub;
global using DSharpPlus.Entities;
using Dolo.Authentication;
using Dolo.Bot.Log.Hub.Handler;
using Microsoft.Extensions.Logging;

var botToken = Authenticator.GetAuthValue("LogToken");
Hub.Discord = DiscordClientBuilder.CreateDefault(botToken!, DiscordIntents.All)
    .ConfigureRestClient(a => a.Timeout = new(0, 0, 30))
    .ConfigureLogging(a => a.AddConsole())
    .ConfigureEventHandlers(a => {
        a.HandleGuildMemberAdded((_, eventArgs) => eventArgs.InvokeAsync());
        a.<PERSON>le<PERSON>essageCreated((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleMessageUpdated((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleMessageDeleted((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleGuildMemberRemoved((_, eventArgs) => eventArgs.InvokeAsync());
        a.<PERSON>le<PERSON>uildDownloadCompleted((_, _) => {
            Console.WriteLine("Ready ...");
            return Task.CompletedTask;
        });
    }).DisableDefaultLogging()
    .Build();

await Hub.Discord.ConnectAsync(new("analyzer", DiscordActivityType.Playing), DiscordUserStatus.Idle);
await Task.Delay(-1);