﻿using Microsoft.Extensions.Logging;
using Dolo.Pluto.Shard;
using Dolo.Pluto.Tool.Pixler.Services;
namespace Dolo.Pluto.Tool.Pixler;

public static class MauiProgram
{
	[System.Runtime.InteropServices.DllImport("kernel32.dll")]
    private static extern bool AllocConsole();

	public static MauiApp CreateMauiApp()
	{

#if !RELEASE
        AllocConsole();
#endif

        var config = new PixlerConfiguration();
		var builder = MauiApp.CreateBuilder();
		builder
			.UseMauiApp<App>()
			.ConfigureSharedWebHost(config)
			.ConfigureFonts(fonts =>
			{
				fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
            }); builder.Services.AddSharedServices(config);
        builder.Services.AddMauiBlazorWebView();

        // Auto-discover and register all IService implementations
        builder.Services.AddAutoServices();

        // Register initialization screen dependencies
        builder.Services.AddInitializationServices<MainService>();

        builder.Services.AddLogging(logging => {
			logging.ClearProviders();
			logging.AddConsole();
			logging.AddDebug();
			logging.SetMinimumLevel(LogLevel.Information);
		});

#if DEBUG
        builder.Services.AddBlazorWebViewDeveloperTools();
		builder.Logging.AddDebug();

#endif

        AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
		{
			Console.WriteLine($"Unhandled exception: {e.ExceptionObject}");
		};

		return builder.Build();
	}
}
