<html>
<script src="https://cdn.tailwindcss.com"></script>
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">

<style>
    body {
        font-family: 'Inter', sans-serif;
        background: #f0f2f5;
        min-height: 100vh;
        display: grid;
        place-items: center;
    }
    
    .card-tabs::-webkit-scrollbar {
        display: none;
    }
    
    .stat-value {
        @apply font-semibold text-gray-900;
    }
    
    .custom-scrollbar {
        scrollbar-width: thin;
        scrollbar-color: #e2e8f0 transparent;
    }
    
    .custom-scrollbar::-webkit-scrollbar {
        width: 4px;
    }
    
    .custom-scrollbar::-webkit-scrollbar-thumb {
        background-color: #e2e8f0;
        border-radius: 4px;
    }
    
    .star-level {
        clip-path: polygon( 50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
    }
    
    .star-inner {
        background: radial-gradient(circle at center, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 70%);
    }
    
    .star-glow {
        background: radial-gradient(circle at center, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.4) 25%, rgba(255, 255, 255, 0) 70%);
    }
    
    .star-shine {
        background: linear-gradient(45deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0) 100%);
    }
    
    .dark-card {
        background: linear-gradient(135deg, #13151a 0%, #1f1f3a 100%);
    }
    
    .dark-glow {
        box-shadow: 0 0 30px rgba(91, 33, 182, 0.2);
    }
</style>

<div class="w-[800px] dark-card rounded-2xl shadow-xl flex flex-col overflow-hidden dark-glow border border-purple-800/20">
    <!-- Replace the Header Section -->
    <div class="relative bg-gradient-to-r from-purple-800/10 via-purple-900/5 to-transparent border-b border-purple-700/20">
        <!-- Background Decorative Element -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute -right-4 -top-10 w-48 h-48 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-full blur-2xl"></div>
        </div>

        <!-- Content -->
        <div class="relative p-5">
            <div class="flex gap-4">
                <!-- Avatar Section -->
                <div class="relative flex-shrink-0">
                    <img src="http://snapshots.mspcdns.com/v1/MSP/de/snapshot/moviestar/3.jpg" class="w-20 h-20 rounded-2xl object-cover shadow-md border-2 border-purple-800/30">
                    <div class="absolute -top-1 -right-1 px-2 py-0.5 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full shadow-sm border-2 border-purple-900/50">
                        <span class="text-[10px] text-white font-semibold tracking-wide">VIP</span>
                    </div>
                </div>

                <!-- Info Section -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between">
                        <!-- Name and Tags -->
                        <div>
                            <h1 class="text-2xl font-bold text-purple-200 tracking-tight mb-1">MovieStarPlanet</h1>
                            <div class="flex items-center gap-2 mb-2">
                                <div class="flex items-center gap-1.5">
                                    <span class="w-2 h-2 rounded-full bg-purple-500"></span>
                                    <span class="text-[11px] font-medium text-purple-700">Server: DE</span>
                                </div>
                                <div class="w-1 h-1 rounded-full bg-gray-300"></div>
                                <span class="text-[11px] text-gray-500">Joined Jan 15, 2023</span>
                                <div class="w-1 h-1 rounded-full bg-gray-300"></div>
                                <span class="text-[11px] text-gray-500">ID: #123456789</span>
                            </div>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="flex items-center gap-3">
                        <div class="flex-1 bg-gray-900/40 rounded-xl p-3 border border-purple-800/20 backdrop-blur-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <span class="text-[10px] font-medium text-purple-300/80 uppercase tracking-wider">Status Update</span>
                                    <span class="text-[10px] text-gray-500 ml-2">• 2h ago</span>
                                </div>
                                <div class="flex items-center gap-1.5">
                                    <div class="w-5 h-5 rounded-full bg-pink-500/20 flex items-center justify-center">
                                        <svg class="w-3 h-3 text-pink-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"></path>
                                        </svg>
                                    </div>
                                    <span class="text-[11px] font-medium text-pink-300">1.2k</span>
                                </div>
                            </div>
                            <p class="text-sm text-purple-200 font-medium mt-1">✨ Living my best virtual life!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area - Adjusted heights -->
    <div class="flex-1 flex h-[calc(100%-64px)]">
        <!-- 64px is header height -->
        <!-- Left Side - Avatar with fixed height -->
        <div class="w-2/5 bg-gradient-to-b from-purple-900/10 via-purple-800/5 to-transparent p-3 relative">
            <!-- Level Star - Top Right -->
            <div class="absolute -right-2 -top-2 z-20">
                <div class="relative">
                    <!-- Outer Glow -->
                    <div class="absolute inset-0 rounded-full bg-purple-400 blur-md opacity-50"></div>

                    <!-- Main Container -->
                    <div class="relative w-[85px] h-[85px] rounded-full bg-gradient-to-br from-purple-600 via-purple-700 to-purple-900 flex items-center justify-center shadow-[0_0_15px_rgba(147,51,234,0.3)] border-2 border-white">
                        <!-- Background Fame Icon -->
                        <img src="https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/fame.png" class="absolute w-12 h-12 opacity-20" alt="Level">

                        <!-- Level Display -->
                        <div class="flex flex-col items-center justify-center relative z-10">
                            <span class="text-[11px] font-medium text-purple-200 tracking-wider -mb-1">LEVEL</span>
                            <span class="text-3xl font-black text-white leading-none tracking-tight drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]">25</span>
                        </div>

                        <!-- Inner Highlight -->
                        <div class="absolute inset-0 rounded-full bg-gradient-to-b from-white/20 to-transparent"></div>
                    </div>
                </div>
            </div>

            <!-- Achievement Badges - Left Side -->
            <div class="absolute left-6 top-[75%] -translate-y-1/2 flex flex-col gap-2 z-10">
                <!-- Judge Badge -->
                <div class="flex items-center gap-2">
                    <div class="w-8 h-8 rounded-lg bg-gray-900/80 flex items-center justify-center shadow-sm border border-purple-800/30 p-1">
                        <img src="https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/badge-judge.png" class="w-full h-full object-contain" alt="Judge">
                    </div>
                    <span class="text-[9px] font-semibold text-purple-200 bg-gray-900/80 px-1.5 py-0.5 rounded-md">Judge</span>
                </div>

                <!-- Jury Badge -->
                <div class="flex items-center gap-2">
                    <div class="w-8 h-8 rounded-lg bg-gray-900/80 flex items-center justify-center shadow-sm border border-purple-800/30 p-1">
                        <img src="https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/badge-jury.png" class="w-full h-full object-contain" alt="Jury">
                    </div>
                    <span class="text-[9px] font-semibold text-purple-200 bg-gray-900/80 px-1.5 py-0.5 rounded-md">Jury</span>
                </div>

                <!-- Celebrity Badge -->
                <div class="flex items-center gap-2">
                    <div class="w-8 h-8 rounded-lg bg-gray-900/80 flex items-center justify-center shadow-sm border border-purple-800/30 p-1">
                        <img src="https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/badge-celeb.png" class="w-full h-full object-contain" alt="Celebrity">
                    </div>
                    <span class="text-[9px] font-semibold text-purple-200 bg-gray-900/80 px-1.5 py-0.5 rounded-md">Celebrity</span>
                </div>
            </div>

            <div class="relative w-full h-[580px] rounded-xl overflow-hidden bg-gradient-to-b from-gray-950/70 to-gray-900/50 backdrop-blur-sm p-2 border border-purple-700/20">
                <img src="http://snapshots.mspcdns.com/v1/MSP/de/snapshot/fullsizemoviestar/3.jpg" class="h-full w-full object-contain object-top">
                <div class="absolute bottom-0 inset-x-0 bg-gray-950/90 backdrop-blur-sm p-3 border-t border-purple-700/20">
                    <div class="flex items-center justify-center gap-2 text-xs">
                        <span class="font-medium text-purple-300">💎 Outfit Value:</span>
                        <span class="font-bold text-purple-200">50,000 SC</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Stats -->
        <div class="w-3/5 p-4 flex flex-col h-full">
            <!-- Redesigned Currency Stats -->
            <div class="space-y-2 mb-3">
                <!-- Diamonds and StarCoins Row -->
                <div class="grid grid-cols-2 gap-2">
                    <!-- Diamonds -->
                    <div class="bg-gray-950/50 rounded-lg p-3 border border-purple-700/20">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <img src="https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/diamond.png" class="w-5 h-5" alt="Diamonds">
                                <span class="text-xs font-medium text-purple-300">Diamonds</span>
                            </div>
                            <span class="text-sm font-bold text-purple-200">25,000</span>
                        </div>
                    </div>

                    <!-- StarCoins -->
                    <div class="bg-gray-950/50 rounded-lg p-3 border border-purple-700/20">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <img src="https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/starcoins.png" class="w-5 h-5" alt="StarCoins">
                                <span class="text-xs font-medium text-amber-300">StarCoins</span>
                            </div>
                            <span class="text-sm font-bold text-amber-200">1,500,000</span>
                        </div>
                    </div>
                </div>

                <!-- Fame Full Width with Progress Bar -->
                <div class="bg-gray-950/50 rounded-lg p-3 border border-purple-700/20">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center gap-2">
                            <img src="https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/fame.png" class="w-5 h-5" alt="Fame">
                            <span class="text-xs font-medium text-pink-300">Fame Level 25</span>
                        </div>
                    </div>
                    <div class="relative h-6 bg-gray-900 rounded-lg overflow-hidden">
                        <div class="absolute inset-0 w-[65%] bg-gradient-to-r from-pink-600 to-purple-600"></div>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-xs font-bold text-white drop-shadow-[0_1px_1px_rgba(0,0,0,0.5)]">
                                500,000 / 750,000
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Replace the Stats sections -->
            <div class="flex-1 overflow-hidden flex flex-col">
                <div class="custom-scrollbar overflow-y-auto flex-1 space-y-2 pr-2">
                    <!-- Statistics -->
                    <div class="bg-gray-950/50 rounded-lg p-2.5 border border-purple-700/20 hover:bg-gray-900/50 transition-colors">
                        <h3 class="text-xs font-semibold text-purple-300/90 mb-2">Statistics</h3>
                        <div class="grid grid-cols-2 gap-3 text-xs">
                            <div class="flex justify-between">
                                <span class="text-gray-400">👥 Friends</span>
                                <span class="stat-value text-gray-300">2,500/3,000</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">💫 VIP Friends</span>
                                <span class="stat-value text-gray-300">500</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">👁️ Profile Views</span>
                                <span class="stat-value text-gray-300">50,000</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">⭐ Room Likes</span>
                                <span class="stat-value text-gray-300">1,234</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">💰 Fortune</span>
                                <span class="stat-value text-gray-300">100,000</span>
                            </div>
                            <div class="col-span-2 border-t border-gray-800 pt-2 mt-1">
                                <div class="flex flex-col gap-1.5">
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">🕒 Last Login</span>
                                        <span class="stat-value text-gray-300">2024-01-15</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">📅 Created At</span>
                                        <span class="stat-value text-gray-300">2023-01-15</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">⏳ Inactive Since</span>
                                        <span class="stat-value text-gray-300">2 days ago</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Beauty -->
                    <div class="bg-gray-950/50 rounded-lg p-2.5 border border-purple-700/20 hover:bg-gray-900/50 transition-colors">
                        <h3 class="text-xs font-semibold text-purple-300/90 mb-2">Beauty</h3>
                        <div class="grid grid-cols-2 gap-3 text-xs">
                            <div class="flex justify-between">
                                <span class="text-gray-400">👁️ Eye Color</span>
                                <span class="stat-value text-gray-300">Blue</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">👁️ Eye ID</span>
                                <span class="stat-value text-gray-300">#12345</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">💄 Mouth Color</span>
                                <span class="stat-value text-gray-300">Pink</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">💄 Mouth ID</span>
                                <span class="stat-value text-gray-300">#67890</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">✨ Eye Shadow</span>
                                <span class="stat-value text-gray-300">Purple</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">✨ Shadow ID</span>
                                <span class="stat-value text-gray-300">#11223</span>
                            </div>
                            <div class="flex justify-between col-span-2">
                                <span class="text-gray-400">🎭 Skin Color</span>
                                <span class="stat-value text-gray-300">Fair</span>
                            </div>
                        </div>
                    </div>

                    <!-- VIP Status -->
                    <div class="bg-gray-950/50 rounded-lg p-2.5 border border-purple-700/20 hover:bg-gray-900/50 transition-colors">
                        <h3 class="text-xs font-semibold text-purple-300/90 mb-2">VIP Status</h3>
                        <div class="grid grid-cols-2 gap-3 text-xs">
                            <div class="flex justify-between">
                                <span class="text-gray-400">⭐ Total VIP Days</span>
                                <span class="text-gray-300">365</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">📅 Purchased</span>
                                <span class="text-gray-300">2023-01-15</span>
                            </div>
                        </div>
                    </div>

                    <!-- Collections -->
                    <div class="bg-gray-950/50 rounded-lg p-2.5 border border-purple-700/20 hover:bg-gray-900/50 transition-colors">
                        <h3 class="text-xs font-semibold text-purple-300/90 mb-2">Collections</h3>
                        <div class="grid grid-cols-2 gap-3 text-xs">
                            <div class="flex justify-between">
                                <span class="text-gray-400">👗 Clothes</span>
                                <span class="stat-value text-gray-300">5,000</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">⭐ Rare Items</span>
                                <span class="stat-value text-gray-300">250</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">📸 Pictures</span>
                                <span class="stat-value text-gray-300">1,500</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">📓 Artbooks</span>
                                <span class="stat-value text-gray-300">100</span>
                            </div>
                        </div>
                    </div>

                    <!-- Character Details -->
                    <div class="bg-gray-950/50 rounded-lg p-2.5 border border-purple-700/20 hover:bg-gray-900/50 transition-colors">
                        <h3 class="text-xs font-semibold text-purple-300/90 mb-2">Character Details</h3>
                        <div class="grid grid-cols-2 gap-3 text-xs">
                            <div class="flex justify-between">
                                <span class="text-gray-400">👁️ Eye Color</span>
                                <span class="stat-value text-gray-300">Blue</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">🎭 Skin Color</span>
                                <span class="stat-value text-gray-300">Fair</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">💄 Mouth Color</span>
                                <span class="stat-value text-gray-300">Pink</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">✨ Eye Shadow</span>
                                <span class="stat-value text-gray-300">Purple</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</html>