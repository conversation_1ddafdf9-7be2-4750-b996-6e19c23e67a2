﻿namespace Dolo.Bot.Log.Hub;

public static class HubCache
{
    private static readonly Dictionary<ulong, DiscordMessage> Messages = new();
    /// <summary>
    ///     Adds a message to the cache.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="message"></param>
    public static void AddMessage(ulong id, DiscordMessage message)
    {
        if (Messages.ContainsKey(id))
            Messages[id] = message;
        else
            Messages.Add(id, message);
    }

    /// <summary>
    ///     Removes a message from the cache.
    /// </summary>
    /// <param name="id"></param>
    public static void RemoveMessage(ulong id)
    {
        if (Messages.ContainsKey(id))
            Messages.Remove(id);
    }

    /// <summary>
    ///     Get a message from the cache.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static DiscordMessage? GetMessage(ulong id)
        => Messages.ContainsKey(id) ? Messages[id] : null;
}