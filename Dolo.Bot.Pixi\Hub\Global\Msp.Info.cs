﻿using Dolo.Core.Discord;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("info")]

[Description("info about the bot")]
    public async Task InfoAsync(SlashCommandContext ctx)
    {
        await ctx.LogAsync("/msp info");
        await ctx.TryDeferAsync();

        var guilds = Hub.Discord.Guilds;
        var members =
            guilds.SelectMany(x => x.Value.Members).Count();
        await ctx.TryEditResponseAsync(await HubEmbed.InfoAsync(guilds.Count, members + 550000, ctx.Guild));
    }
}