﻿using Dolo.Core.Extension;
using System.Text;
namespace Dolo.Bot.Log.Hub;

internal class HubEmbed
{
    public static DiscordEmbed Joined(DiscordMember member) => new DiscordEmbedBuilder()
        .WithTitle("Joined")
        .WithColor(new("63FE5F"))
        .WithThumbnail(member.AvatarUrl)
        .WithDescription(new StringBuilder()
            .AppendLine($"[**User**](https://a) • {member.Mention}")
            .AppendLine($"[**Name**](https://a) • {member.Username}")
            .AppendLine($"[**Created**](https://a) • {member.CreationTimestamp}")
            .AppendLine($"[**Joined**](https://a) • {member.JoinedAt}")
            .ToString()).Build();

    public static DiscordEmbed Left(DiscordMember member) => new DiscordEmbedBuilder()
        .WithTitle("Left")
        .WithColor(new("F92651"))
        .WithThumbnail(member.AvatarUrl)
        .WithDescription(new StringBuilder()
            .AppendLine($"[**User**](https://a) • {member.Mention}")
            .AppendLine($"[**Name**](https://a) • {member.Username}")
            .AppendLine($"[**Created**](https://a) • {member.CreationTimestamp}")
            .AppendLine($"[**Joined**](https://a) • {member.JoinedAt}")
            .ToString()).Build();

    public static DiscordEmbed MessageCreated(DiscordMessage message) => new DiscordEmbedBuilder()
        .WithTitle("Created")
        .WithColor(new("63FE5F"))
        .WithThumbnail(message.Author?.AvatarUrl)
        .WithDescription(new StringBuilder()
            .AppendLine($"[**Name**](https://a) • {message.Author.Username}")
            .AppendLine($"[**User**](https://a) • {message.Author.Mention}")
            .AppendLine($"[**Channel**](https://a) • {message.Channel?.Mention}")
            .AppendLine()
            .AppendLine("**Message**")
            .Append($"```{message.Content.IsNullOrEmptyThen("Attachment/Picture/Embed")}```")
            .ToString()).Build();

    public static DiscordEmbed MessageDeleted(DiscordMessage? message) => new DiscordEmbedBuilder()
        .WithTitle("Deleted")
        .WithColor(new("F92651"))
        .WithThumbnail(message?.Author.AvatarUrl)
        .WithDescription(new StringBuilder()
            .AppendLine($"[**Name**](https://a) • {message?.Author?.Username}")
            .AppendLine($"[**User**](https://a) • {message?.Author?.Mention}")
            .AppendLine($"[**Channel**](https://a) • {message?.Channel.Mention}")
            .AppendLine()
            .AppendLine("**Message**")
            .Append($"```{message?.Content?.IsNullOrEmptyThen("Attachment/Picture/Embed")}```")
            .ToString()).Build();


    public static DiscordEmbed MessageUpdated(DiscordMessage message, DiscordMessage messagebefore) => new DiscordEmbedBuilder()
        .WithTitle("Updated")
        .WithColor(new("FEC456"))
        .WithThumbnail(message.Author.AvatarUrl)
        .WithDescription(new StringBuilder()
            .AppendLine($"[**Name**](https://a) • {message.Author.Username}")
            .AppendLine($"[**User**](https://a) • {message.Author.Mention}")
            .AppendLine($"[**Channel**](https://a) • {message.Channel.Mention}")
            .AppendLine()
            .AppendLine("**Message Before**")
            .Append($"```{messagebefore.Content.IsNullOrEmptyThen("Attachment/Picture/Embed")}```")
            .AppendLine("**Message After**")
            .Append($"```{message.Content.IsNullOrEmptyThen("Attachment/Picture/Embed")}```")
            .ToString()).Build();
}