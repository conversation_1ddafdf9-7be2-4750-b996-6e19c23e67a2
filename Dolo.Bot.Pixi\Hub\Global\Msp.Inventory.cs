﻿using Dolo.Core.Discord;
using Dolo.Planet;
using Dolo.Planet.Enums;
using System.Text;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("inventory")]
    [Description("dump full inventory")]
    public async Task InventoryAsync(SlashCommandContext ctx,
        [Description("player server")] Server server,
        [Description("player username")] string username)
    {
        await ctx.LogAsync($"/msp inventory {username} {server}");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync(true)) return;

        // check if the shard is ready to use 
        var msp = await ctx.IsShardReadyAsync(server);
        if (msp is null) return;

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **Searching for `{username}` ..**");

        // get the id of the user if the connection failed return
        var user = await msp.GetActorIdAsync(username);
        if (!user.Success)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Interaction failed with response `{user.GetStatusCode()}`**");
            return;
        }

        // return if the user is not available
        if (!user.IsAvailable)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » {MspClientUtil.GetServerDiscordFlag(server)} » **User `{username}` is not available**");
            return;
        }

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **Dumping inventory of `{username}` ...**");
        var items = await msp.GetActorItemsAsync(user.Id);
        var builder = new StringBuilder();
        foreach (var c in items)
        {
            builder.AppendLine(c.Cloth?.Name?.ToUpper());
            builder.AppendLine($"• RelId: {c.Id}");
            builder.AppendLine($"• Color: {c.Color ?? "none"}");
            builder.AppendLine($"• Swf: {c.Url}");
            builder.AppendLine("• Cloth");
            builder.AppendLine($"  • Id: {c.Cloth?.ThemeId}");
            builder.AppendLine($"  • ThemeId: {c.Cloth?.ThemeId}");
            builder.AppendLine($"  • Category: {c.Cloth?.CategoryName}");
            builder.AppendLine($"  • ShopId: {c.Cloth?.ShopId}");
            builder.AppendLine($"  • LastUpdatedAt: {c.Cloth?.LastUpdated}");
            builder.AppendLine($"  • StarCoins: {c.Cloth?.Price}");
            builder.AppendLine($"  • Diamonds: {c.Cloth?.DiamondsPrice}");
            builder.AppendLine($"  • Is Favourite: {c.IsFav}");
            builder.AppendLine($"  • Is Vip: {c.Cloth?.IsVip}");
            builder.AppendLine($"  • Is Head: {c.Cloth?.IsHead}");
            builder.AppendLine($"  • Is Hair: {c.Cloth?.IsHair}");
            builder.AppendLine($"  • Is Top: {c.Cloth?.IsTop}");
            builder.AppendLine($"  • Is Bottom: {c.Cloth?.IsBottom}");
            builder.AppendLine($"  • Is Foot: {c.Cloth?.IsFoot}");
            builder.AppendLine($"  • Is Accessories: {c.Cloth?.IsAccessories}");
            builder.AppendLine($"  • Is Stuff: {c.Cloth?.IsStuff}");
            builder.AppendLine($"  • Is New: {c.Cloth?.IsNew != 0}");
            builder.AppendLine();
        }

        using var mem = new MemoryStream(Encoding.UTF8.GetBytes(builder.ToString()));
        await ctx.TryEditResponseAsync(new DiscordWebhookBuilder()
            .WithContent($"{HubEmoji.Pluto} » {MspClientUtil.GetServerDiscordFlag(server)} » **Dumped inventory of `{username}`**")
            .AddFile($"inventory-{user.Username}.arm", mem));
    }
}