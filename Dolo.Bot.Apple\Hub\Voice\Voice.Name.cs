﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Voice;

public partial class Voice
{
    [Command("name")]

[Description("change the voice channel name")]
    public async Task VoiceNameAsync(SlashCommandContext ctx, [Description("the new channel name")] string text)
    {
        if (Hub.Guild is null)
            return;

        // defer the message
        await ctx.Interaction.DeferAsync();

        // check if the text length is too long
        if ($"🌸 » {text}".Length > 96)
        {
            await ctx.TryEditResponseAsync("The name is too long.");
            return;
        }

        // print error if the topic is not the voice system
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryEditResponseAsync("This command can only be used in voice channels.");
            return;
        }

        // get the channel database entry
        var usr = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (usr is null)
            return;

        // try to get the channel 
        var channel = Hub.Guild.TryGetChannel(usr.Channel);
        if (channel is null)
            return;

        // check if the limit is the same
        if (channel.Name.Equals($"🌸 » {text}"))
        {
            await ctx.TryEditResponseAsync($"The name is already set to `{text}`.");
            return;
        }

        // check if the user is allowed to perform the command
        if (usr.Owner != ctx.User.Id && !usr.Moderator.Contains(ctx.User.Id))
        {
            await ctx.TryEditResponseAsync("You are not a channel moderator.");
            return;
        }

        // modify the channel name
        await channel.ModifyAsync(a => a.Name = $"🌸 » {text}");

        // send the message
        await ctx.TryEditResponseAsync($"The channel name has been set to `{text}`.");
    }
}