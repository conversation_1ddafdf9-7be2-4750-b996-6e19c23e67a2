<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net10.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>preview</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Binance.Net" Version="11.2.0" />
        <PackageReference Include="Bogus" Version="35.6.3" />
        <PackageReference Include="DSharpPlus" Version="5.0.0-nightly-02532" />
        <PackageReference Include="DSharpPlus.Commands" Version="5.0.0-nightly-02532" />
        <PackageReference Include="DSharpPlus.Interactivity" Version="5.0.0-nightly-02532" />
        <PackageReference Include="DSharpPlus.Rest" Version="5.0.0-nightly-02504" />
        <PackageReference Include="DSharpPlus.SlashCommands" Version="5.0.0-nightly-02532" />
        <PackageReference Include="DSharpPlus.VoiceNext" Version="5.0.0-nightly-02532" />
        <PackageReference Include="Fluxzy.Core" Version="1.29.16" />
        <PackageReference Include="JsonSubTypes" Version="2.0.1" />
        <PackageReference Include="MethodTimer.Fody" Version="3.2.3" />
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="10.0.0-preview.6.25358.103" />
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.MessagePack" Version="10.0.0-preview.6.25358.103" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="10.0.0-preview.6.25358.103" />
        <PackageReference Include="Microsoft.Extensions.Logging.Configuration" Version="10.0.0-preview.6.25358.103" />
        <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="10.0.0-preview.6.25358.103" />
        <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="10.0.0-preview.6.25358.103" />
        <PackageReference Include="Microsoft.JSInterop" Version="10.0.0-preview.6.25358.103" />
        <PackageReference Include="MongoDB.Driver" Version="3.4.1" />
        <PackageReference Include="MongoDB.Driver.GridFS" Version="2.30.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="plist-cil" Version="2.2.0" />
        <PackageReference Include="Polly" Version="8.6.2" />
        <PackageReference Include="Polly.Contrib.WaitAndRetry" Version="1.1.1" />
        <PackageReference Include="protobuf-net" Version="3.2.52" />
        <PackageReference Include="System.Drawing.Common" Version="10.0.0-preview.6.25358.103" />
        <PackageReference Include="System.Management" Version="10.0.0-preview.6.25358.103" />
        <PackageReference Include="Titanium.Web.Proxy" Version="3.2.2-beta" />
    </ItemGroup>

</Project>
