﻿using Dolo.Core.Discord;
using Dolo.Pluto.Shard.Bot.Pixi;
using MongoDB.Driver;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Blacklist
    {
        [Command("ban")]
        [Description("add a user to blacklist")]
        public async Task AddAsync(SlashCommandContext ctx,
            [Description("the user that should be blacklisted")] DiscordUser user)
        {
            if (ctx.User.Id != 440584675740876810) return;

            await ctx.TryDeferAsync(true);
            await ctx.TryEditResponseAsync("Trying to blacklist user ..");
            var config = await Database.Mongo.PixiSettings.GetFirstAsync();
            if (config is null) return;

            if (config.HasBlockUser(user.Id))
            {
                await ctx.TryEditResponseAsync("User is already blacklisted");
                return;
            }

            config.BlockUser(user.Id);

            await Database.Mongo.PixiSettings.UpdateAsync(Builders<PixiSettings>.Filter.Empty,
            Builders<PixiSettings>.Update.Set(x => x.Users, config.Users));
            await ctx.TryEditResponseAsync($"User `{user.Mention}` has been blacklisted");
        }
    }
}