<!-- Floating Exchange Preview Window -->
<div class="fixed bottom-4 left-4 w-64 bg-[#2E3033] border border-border-strong rounded-lg shadow-2xl z-50 transition-all duration-300 ease-out">
    <!-- Header -->
    <div @onclick="ToggleExpanded" class="flex items-center justify-between p-3 @(IsExpanded ? "bg-[#1F2023] rounded-t-lg" : "bg-[#1F2023] rounded-lg hover:bg-[#2E3033] hover:scale-105") cursor-pointer transition-all duration-200">
        <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1.5 text-text-main" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clip-rule="evenodd" />
            </svg>
            <h4 class="text-xs font-bold text-text-main">Live Exchange</h4>
        </div>
        <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-text-secondary @(IsExpanded ? "rotate-180" : "")" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
        </div>
    </div>

    <!-- Content -->
    <div class="overflow-hidden transition-all duration-300 ease-out @(IsExpanded ? "max-h-96 p-3" : "max-h-0 p-0")">
        @if (IsActive)
        {
            <!-- Current Item & Status -->
            <div class="mb-3">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-xs font-medium text-text-main">Current Exchange</span>
                    <span class="text-xs px-1.5 py-0.5 bg-warning/20 text-warning rounded">Active</span>
                </div>
                
                <div class="flex items-center mb-2">
                    <div class="w-6 h-6 bg-[#3A3C3F] rounded-md flex items-center justify-center mr-2">
                        <span class="text-sm">@CurrentItem</span>
                    </div>
                    <div>
                        <div class="text-xs font-medium text-text-main">@CurrentItemName</div>
                        <div class="text-xs text-text-secondary">@CurrentStatus</div>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="w-full bg-[#1F2023] rounded-full h-1.5 mb-2">
                    <div class="bg-warning h-1.5 rounded-full transition-all duration-500" style="width: @(Progress)%"></div>
                </div>
                <div class="text-xs text-text-secondary text-center">@Progress% Complete</div>
            </div>

            <!-- Account Status -->
            <div class="grid grid-cols-2 gap-2 mb-3">
                <div class="bg-[#1F2023] rounded-lg p-2">
                    <div class="flex items-center mb-1">
                        <div class="w-3 h-3 rounded-full bg-[#3A3C3F] flex items-center justify-center mr-1">
                            <span class="text-xs font-bold text-text-main">A</span>
                        </div>
                        <span class="text-xs text-text-main">Sender</span>
                    </div>
                    <div class="text-xs text-@(AccountAStatus == "Sending" ? "warning" : "text-secondary")">@AccountAStatus</div>
                </div>
                
                <div class="bg-[#1F2023] rounded-lg p-2">
                    <div class="flex items-center mb-1">
                        <div class="w-3 h-3 rounded-full bg-[#3A3C3F] flex items-center justify-center mr-1">
                            <span class="text-xs font-bold text-text-main">B</span>
                        </div>
                        <span class="text-xs text-text-main">Receiver</span>
                    </div>
                    <div class="text-xs text-@(AccountBStatus == "Receiving" ? "warning" : "text-secondary")">@AccountBStatus</div>
                </div>
            </div>

            <!-- Data Transfer Animation -->
            <div class="relative h-8 bg-[#1F2023] rounded-lg mb-3 overflow-hidden">
                <div class="absolute inset-0 flex items-center justify-center">
                    <span class="text-xs text-text-secondary">Data Transfer</span>
                </div>
                @if (ShowDataTransfer)
                {
                    <div class="absolute top-1/2 transform -translate-y-1/2 w-1 h-2 bg-warning rounded-full animate-pulse" style="left: @(DataTransferPosition)%; transition: left 1s ease-in-out;"></div>
                }
            </div>

            <!-- Control Buttons -->
            <div class="flex gap-2">
                <button @onclick="PauseExchange" class="flex-1 bg-[#3A3C3F] hover:bg-[#4A4C4F] text-text-main px-2 py-1 rounded text-xs transition-all">
                    @(IsPaused ? "Resume" : "Pause")
                </button>
                <button @onclick="CancelExchange" class="flex-1 bg-error/20 hover:bg-error/30 text-error px-2 py-1 rounded text-xs transition-all">
                    Cancel
                </button>
            </div>
        }
        else
        {
            <!-- Inactive State -->
            <div class="text-center py-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mx-auto mb-2 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clip-rule="evenodd" />
                </svg>
                <p class="text-xs text-text-secondary">No active exchange</p>
                <p class="text-xs text-text-secondary mt-1">Start an exchange to see live progress</p>
            </div>
        }
    </div>
</div>
