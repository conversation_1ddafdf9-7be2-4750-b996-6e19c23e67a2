﻿using Dolo.Core.Discord;
using DSharpPlus.EventArgs;
namespace Dolo.Bot.Log.Hub.Handler;

public static class MessageDelete
{
    public static async Task InvokeAsync(this MessageDeletedEventArgs e)
    {
        // get the message from the cache
        var msg = HubCache.GetMessage(e.Message.Id);

        // remove it from the cache
        HubCache.RemoveMessage(e.Message.Id);

        // if the message is null, return
        if (msg is null)
            return;

        // log the message
        await HubChannel.Log!.TrySendMessageAsync(HubEmbed.MessageDeleted(msg));
    }
}