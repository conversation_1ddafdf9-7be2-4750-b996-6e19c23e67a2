﻿using Dolo.Core.Discord;
using Dolo.Planet;
using Dolo.Planet.Entities;
using System.Text;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("highscore")]

[Description("get a list of top 25 highscore users from all server")]
    public async Task HighscoreAsync(SlashCommandContext ctx)
    {
        await ctx.LogAsync($"/msp highscore");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync()) return;

        // check if the shard is ready to use 
        var shard = Hub.MspShard?.GetWorked();
        if (shard is null)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **No server are ready to use.**");
            return;
        }

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » **Fetching highscore users...**");

        var highscores = new MspList<MspActor>();
        foreach (var msp in shard)
        {
            var highscore = await msp.GetHighscoreActorsAsync(a => 
                         a.UseCount(5));
            if (highscore.Any())
                    highscores.AddRange(highscore);
        }

        if (!highscores.Any())
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **No highscore users found.**");
            return;
        }

        var builder = new StringBuilder();
        var filteredHighscore = highscores.OrderByDescending(a => a.Fame).Take(25).ToList();
        for (var i = 0; i < filteredHighscore.Count; i++)
        {
            builder.AppendLine($"{MspClientUtil.GetServerDiscordFlag(filteredHighscore[i].Server)} » {HubEmoji.Fame} » **#{i + 1}** » [**{filteredHighscore[i].Level}**](https://a) » **{filteredHighscore[i].Username}** » [{filteredHighscore[i].Fame:N0}](https://a)");
            if (i % 5 == 0) builder.AppendLine();
        }

        await ctx.TryEditResponseAsync(await HubEmbed.HighscoreAsync(ctx.Guild, builder));
    }
}