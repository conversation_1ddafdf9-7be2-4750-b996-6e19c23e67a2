<!-- Item Selection -->
<div class="mb-4 mt-2">
    <div class="flex items-center mb-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-text-main" viewBox="0 0 20 20" fill="currentColor">
            <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
        </svg>
        <h3 class="text-xs font-bold text-text-main font-jakarta">Item Selection</h3>
    </div>

    <div class="bg-[#2E3033] border border-border-strong rounded-lg p-3">
        <div class="flex items-center gap-2 mb-3">
            <button @onclick="LoadItems" class="bg-[#3A3C3F] hover:bg-[#4A4C4F] text-text-main px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                Load Items
            </button>
        </div>

        <!-- Inventory Grid -->
        <div class="grid grid-cols-6 gap-2 mb-3 max-h-32 overflow-y-auto">
            @foreach (var item in Items)
            {
                <div @onclick="@(() => SelectItem(item))"
                     class="@(SelectedItem?.Name == item.Name ? "bg-[#3A3C3F] border-2 border-border-focus" : "bg-[#1F2023] border border-border-base hover:bg-[#2E3033]") rounded-lg p-2 flex flex-col items-center cursor-pointer transition-all">
                    <span class="text-lg mb-1">@item.Icon</span>
                    <span class="text-xs @(SelectedItem?.Name == item.Name ? "text-text-main" : "text-text-secondary") text-center leading-tight">@item.Name</span>
                </div>
            }
        </div>

        @if (SelectedItem != null)
        {
            <!-- Selected Item Display & Start Button -->
            <div class="flex items-center gap-3 pt-2 border-t border-border-strong">
                <div class="flex items-center flex-1">
                    <div class="w-8 h-8 bg-[#3A3C3F] border border-border-focus rounded-lg flex items-center justify-center mr-2">
                        <span class="text-sm">@SelectedItem.Icon</span>
                    </div>
                    <div>
                        <div class="text-xs font-medium text-text-main">@SelectedItem.Name</div>
                        <div class="text-xs text-text-secondary">Selected for exchange</div>
                    </div>
                </div>
                <button @onclick="StartExchange" disabled="@IsExchangeInProgress"
                        class="bg-[#3A3C3F] hover:bg-[#4A4C4F] disabled:bg-[#2E3033] disabled:cursor-not-allowed text-text-main px-4 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center border border-border-strong">
                    @if (IsExchangeInProgress)
                    {
                        <svg class="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>@ExchangeProgress%</span>
                    }
                    else
                    {
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span>Start Exchange</span>
                    }
                </button>
            </div>
        }

        @if (IsExchangeInProgress)
        {
            <!-- Exchange Progress Indicator -->
            <div class="mt-3 p-3 bg-[#1F2023] border border-border-base rounded-lg">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-xs font-medium text-text-main">Exchange Progress</span>
                    <span class="text-xs text-text-secondary">@ExchangeProgress%</span>
                </div>
                <div class="w-full bg-[#2E3033] rounded-full h-1.5 mb-2">
                    <div class="bg-warning h-1.5 rounded-full transition-all duration-300" style="width: @(ExchangeProgress)%"></div>
                </div>
                <p class="text-xs text-text-secondary">@ExchangeStatus</p>
            </div>
        }
    </div>
</div>
