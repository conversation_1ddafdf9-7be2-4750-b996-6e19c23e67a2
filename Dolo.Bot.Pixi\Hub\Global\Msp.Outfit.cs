﻿using Dolo.Core.Discord;
using Dolo.Planet;
using Dolo.Planet.Enums;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("Outfit")]

[Description("get outfit of info of an player")]
    public async Task OutfitAsync(SlashCommandContext ctx,
        [Description("player server")] Server server,
        [Description("player username")] string username)
    {
        await ctx.LogAsync($"/msp outfit {username} {server}");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync()) return;

        // check if the shard is ready to use 
        var msp = await ctx.IsShardReadyAsync(server);
        if (msp is null) return;

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **Loading cloth for `{username}`...**");

        // get the id of the user if the connection failed return
        var user = await msp.GetActorAsync(username);
        if (!user.Success)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Interaction failed with response `{user.GetStatusCode()}`**");
            return;
        }

        // return if the user is not available
        if (!user.IsAvailable)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » {MspClientUtil.GetServerDiscordFlag(server)} » **User `{username}` is not available**");
            return;
        }

        // check if clothes exist
        if (!user.ClothRel.Any())
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » {MspClientUtil.GetServerDiscordFlag(server)} » **User `{username}` does not have any cloth**");
            return;
        }

        // send response
        await ctx.TryEditResponseAsync(await HubEmbed.ClothAsync(ctx.Guild, user));
    }
}